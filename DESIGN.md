# 酒店管理系统设计文档

## 1. 系统架构设计

### 1.1 整体架构
系统采用Flask Web框架开发，使用MVC架构模式：
- Model（模型层）：处理数据和业务逻辑
- View（视图层）：负责用户界面展示
- Controller（控制层）：处理用户请求和业务逻辑

### 1.2 技术栈
- 后端框架：Flask
- 前端框架：Bootstrap 5
- 数据库：SQLite
- 图表库：Chart.js
- ORM：Flask-SQLAlchemy

### 1.3 目录结构
```
hotel-management-system/
├── app/                    # 应用主目录
│   ├── __init__.py         # 应用初始化
│   ├── models.py           # 数据模型
│   ├── main/               # 主模块
│   │   ├── __init__.py
│   │   ├── views.py        # 主视图
│   │   └── utils.py        # 工具函数
│   ├── departments/        # 部门模块
│   ├── employees/          # 员工模块
│   ├── rooms/              # 房间模块
│   ├── finance/            # 财务模块
│   ├── achievements/       # 成就模块
│   ├── marketing/          # 营销模块
│   ├── hotel/              # 酒店模块
│   └── templates/          # 模板文件
├── static/                 # 静态文件
├── instance/               # 数据库文件
├── config.py               # 配置文件
├── run.py                  # 应用入口
└── requirements.txt        # 依赖包
```

## 2. 数据库设计

### 2.1 数据表结构

#### 2.1.1 酒店表（Hotel）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| name | String(100) | 酒店名称 |
| level | Integer | 酒店等级（1-9星） |
| date | Date | 当前日期 |
| money | Integer | 资金余额 |
| time_running | Boolean | 时间运行状态 |
| days_elapsed | Integer | 运行天数 |
| satisfaction | Float | 客户满意度(0-100) |
| reputation | Integer | 声望值 |
| reputation_level | String(50) | 声望等级 |

#### 2.1.2 员工表（Employee）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| hotel_id | Integer | 外键，关联酒店 |
| name | String(50) | 员工姓名 |
| department | String(50) | 所属部门 |
| level | String(20) | 员工等级 |
| base_salary | Integer | 基础工资 |
| salary | Integer | 实际工资 |
| hire_date | Date | 入职日期 |
| years_worked | Integer | 工龄 |

#### 2.1.3 部门表（Department）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| hotel_id | Integer | 外键，关联酒店 |
| name | String(50) | 部门名称 |
| is_unlocked | Boolean | 是否已解锁 |
| unlock_cost | Integer | 解锁费用 |

#### 2.1.4 房间表（Room）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| hotel_id | Integer | 外键，关联酒店 |
| type | String(50) | 房间类型 |
| count | Integer | 房间数量 |
| price | Integer | 房间价格 |

#### 2.1.5 财务记录表（FinancialRecord）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| hotel_id | Integer | 外键，关联酒店 |
| record_date | Date | 记录日期 |
| description | String(200) | 描述 |
| income | Integer | 收入 |
| expense | Integer | 支出 |
| department | String(50) | 所属部门 |

#### 2.1.6 成就表（Achievement）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| hotel_id | Integer | 外键，关联酒店 |
| name | String(100) | 成就名称 |
| description | String(200) | 成就描述 |
| is_unlocked | Boolean | 是否已解锁 |
| reward_claimed | Boolean | 奖励是否已领取 |
| achieved | Boolean | 是否达成 |
| achieved_date | Date | 达成日期 |

#### 2.1.7 游戏设置表（GameSetting）
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 主键 |
| hotel_id | Integer | 外键，关联酒店 |
| key | String(100) | 设置键名 |
| value | Text | 设置值 |

## 3. 用户界面设计

### 3.1 首页设计
首页是系统的核心展示页面，包含以下模块：
- 酒店信息展示区域
- 经营情况统计区域
- 部门繁忙度展示区域
- 入住率图表展示区域
- 最近财务记录展示区域
- 时间控制按钮区域

### 3.2 管理页面设计
各个管理页面采用统一的设计风格：
- 页面标题
- 功能操作区域
- 数据展示表格
- 操作按钮

### 3.3 响应式设计
所有页面都采用响应式设计，适配不同屏幕尺寸。

## 4. 核心算法设计

### 4.1 入住率计算方法
入住率根据房间类型和随机因素计算：
- 单人间：60% ± 20%
- 标准间：70% ± 20%
- 大床房：75% ± 20%
- 家庭房：65% ± 20%
- 商务间：80% ± 20%
- 行政间：85% ± 20%
- 豪华间：90% ± 20%
- 总统套房：95% ± 20%
- 皇家套房：92% ± 20%
- 总统别墅：94% ± 20%
- 皇宫套房：96% ± 20%

入住率会受到以下因素影响：
- 营销活动效果
- 客户满意度
- 酒店声望等级
- 季节性因素
- 随机事件

### 4.2 满意度计算方法
客户满意度基于以下因素计算：

1. 基础满意度：50分
2. 房间等级加成：
   - 皇宫套房：+10分
   - 总统别墅：+8分
   - 皇家套房：+6分
   - 总统套房：+4分
3. 酒店星级加成：每星+1分
4. 部门服务影响：
   - 前台部：影响客户第一印象
   - 客房部：影响住宿体验
   - 餐饮部：影响餐饮满意度
   - 康养部：影响休闲满意度
5. 员工数量与房间数量比例：员工不足时满意度下降
6. 营销活动影响：正面影响满意度
7. 随机事件影响

满意度调整规则：
- 满意度 >= 90：声望值 +5/天
- 满意度 >= 80：声望值 +3/天
- 满意度 >= 70：声望值 +1/天
- 满意度 <= 30：声望值 -5/天
- 满意度 <= 40：声望值 -3/天
- 满意度 <= 50：声望值 -1/天

满意度限制在0-100之间。

### 4.3 声望值计算方法
声望值基于以下因素变化：

1. 每日基础变化：根据满意度计算
2. 成就解锁：解锁成就时获得声望值奖励
3. 财务表现：
   - 日利润 > 10万：声望值 +3
   - 日利润 > 5万：声望值 +2
   - 日利润 > 1万：声望值 +1
4. 酒店等级：
   - 每升一级获得基础声望值
5. 特殊事件影响

声望等级划分：
- 0-500：默默无闻
- 500-2000：小有名气
- 2000-5000：知名酒店
- 5000-10000：著名品牌
- 10000-20000：行业标杆
- 20000-50000：行业领袖
- 50000-100000：国际知名
- 100000+：传奇酒店

### 4.4 收入计算方法

1. 客房收入 = Σ(房间数量 × 房间价格 × 入住率)
2. 餐饮收入 = 入住客户数 × 20 × 酒店等级 + 员工数 × 5 × 酒店等级
3. 康养部收入 = 入住客户数 × 15 × 酒店等级（如果有康养部）
4. 营销部收入 = 营销活动带来的额外入住客户数 × 平均房价

### 4.5 支出计算方法

1. 员工工资：根据员工等级和工龄计算
2. 房间维护费用：每间房间每月100元
3. 营销活动费用：按活动类型收取
4. 其他支出：随机事件导致的支出

## 5. 业务逻辑设计

### 5.1 时间推进机制
- 每次点击"推进一天"按钮，系统时间前进一天
- 时间推进时自动计算当日收入和支出
- 自动更新员工工龄
- 每月1号自动扣除员工工资
- 每年1月1号自动进行员工晋升

### 5.2 数据持久化
- 所有游戏数据都存储在数据库中
- 每次操作后立即保存到数据库
- 支持数据的增删改查操作

### 5.3 成就系统
- 系统初始化时创建所有成就记录
- 每次时间推进时检查成就解锁条件
- 成就解锁后可领取声望值奖励
- 奖励领取后标记为已领取状态

## 6. 安全设计

### 6.1 数据安全
- 使用SQLite数据库存储游戏数据
- 所有数据操作通过ORM进行，防止SQL注入
- 重要操作进行数据验证

### 6.2 应用安全
- 使用Flask框架的安全特性
- 对用户输入进行验证和过滤
- 防止跨站脚本攻击(XSS)
