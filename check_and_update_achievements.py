#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查并更新成就状态
"""

from app import create_app, db
from app.models import Hotel, Achievement, Employee, Room, Department
from app.achievements.views import check_achievements

def update_achievements():
    """更新成就状态"""
    app = create_app()
    
    with app.app_context():
        hotel = Hotel.query.first()
        if not hotel:
            print("❌ 酒店数据未初始化")
            return
        
        print(f"🏨 酒店信息:")
        print(f"  - 名称: {hotel.name}")
        print(f"  - 等级: {hotel.level}星")
        print(f"  - 资金: ¥{hotel.money:,}")
        print(f"  - 声望: {hotel.reputation}")
        print(f"  - 满意度: {hotel.satisfaction:.1f}")
        
        # 获取统计数据
        total_employees = Employee.query.filter_by(hotel_id=hotel.id).count()
        total_rooms = sum(room.count for room in Room.query.filter_by(hotel_id=hotel.id).all())
        unlocked_departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).count()
        
        print(f"  - 员工数: {total_employees}")
        print(f"  - 房间数: {total_rooms}")
        print(f"  - 已解锁部门: {unlocked_departments}")
        
        # 检查成就
        print(f"\n🏆 检查成就状态...")
        newly_achieved = check_achievements(hotel)
        
        if newly_achieved:
            print(f"🎉 新解锁成就: {[a.name for a in newly_achieved]}")
        else:
            print("📝 没有新解锁的成就")
        
        # 显示所有成就状态
        all_achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()
        achieved_count = sum(1 for a in all_achievements if a.achieved)
        
        print(f"\n📊 成就统计:")
        print(f"  - 总成就数: {len(all_achievements)}")
        print(f"  - 已完成: {achieved_count}")
        print(f"  - 完成率: {(achieved_count / len(all_achievements) * 100):.1f}%")
        
        # 显示已完成的成就
        achieved_achievements = [a for a in all_achievements if a.achieved]
        if achieved_achievements:
            print(f"\n✅ 已完成的成就:")
            for achievement in achieved_achievements:
                print(f"  - {achievement.name}: {achievement.description}")
        
        # 显示可能即将完成的成就
        print(f"\n🎯 可能即将完成的成就:")
        if hotel.money >= 100000:
            print(f"  - 初次盈利: 已满足条件 (资金¥{hotel.money:,} >= ¥100,000)")
        if hotel.money >= 1000000:
            print(f"  - 百万富翁: 已满足条件 (资金¥{hotel.money:,} >= ¥1,000,000)")
        if hotel.money >= 10000000:
            print(f"  - 千万富翁: 已满足条件 (资金¥{hotel.money:,} >= ¥10,000,000)")
        if hotel.money >= 100000000:
            print(f"  - 亿万富翁: 已满足条件 (资金¥{hotel.money:,} >= ¥100,000,000)")
        if total_employees >= 1:
            print(f"  - 首位员工: 已满足条件 (员工数{total_employees} >= 1)")
        if hotel.level >= 2:
            print(f"  - 星光初现: 已满足条件 (等级{hotel.level}星 >= 2星)")

if __name__ == "__main__":
    update_achievements()
