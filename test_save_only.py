#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试存档功能
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_save_create():
    """测试创建存档"""
    print("💾 测试创建存档")
    print("-" * 30)
    
    try:
        save_data = {'save_name': 'test_save_' + str(int(time.time()))}
        
        response = requests.post(f"{BASE_URL}/save/create", 
                               json=save_data, 
                               timeout=10)
        
        print(f"响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 创建存档成功")
                return True
            else:
                print(f"❌ 创建存档失败: {data.get('message', '')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_save_list():
    """测试获取存档列表"""
    print("\n📋 测试获取存档列表")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/save/list", timeout=10)
        
        print(f"响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                saves = data.get('saves', [])
                print(f"✅ 获取存档列表成功，共{len(saves)}个存档")
                return True
            else:
                print(f"❌ 获取存档列表失败: {data.get('message', '')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

if __name__ == "__main__":
    print("🔧 存档功能测试")
    print("=" * 40)
    
    time.sleep(2)  # 等待服务器
    
    test_save_create()
    test_save_list()
