# 酒店管理系统部署文档

## 1. 系统要求

### 1.1 硬件要求

- **CPU**: 1 GHz或更高
- **内存**: 512 MB RAM或更高
- **存储**: 100 MB可用磁盘空间
- **网络**: 本地网络连接（可选）

### 1.2 软件要求

- **操作系统**: Windows 7/8/10/11, macOS 10.12或更高版本, Linux (Ubuntu 16.04+/CentOS 7+)
- **Python版本**: Python 3.7或更高版本
- **数据库**: SQLite (已内置，无需额外安装)
- **浏览器**: Chrome, Firefox, Safari, Edge等现代浏览器

## 2. 部署前准备

### 2.1 安装Python

确保系统已安装Python 3.7或更高版本：

```bash
python --version
# 或
python3 --version
```

如果未安装Python，请从[Python官网](https://www.python.org/downloads/)下载并安装。

### 2.2 克隆或下载项目

从代码仓库克隆项目：

```bash
git clone <项目仓库地址>
```

或者直接下载项目压缩包并解压。

### 2.3 安装依赖

进入项目目录并安装所需依赖：

```bash
cd hotel-management-system
pip install -r requirements.txt
```

## 3. 初始化数据库

### 3.1 创建数据库表

运行初始化脚本创建数据库表和初始数据：

```bash
python init_db.py
```

### 3.2 验证数据库初始化

检查是否成功创建了以下数据表：
- hotels (酒店信息表)
- rooms (房间信息表)
- employees (员工信息表)
- departments (部门信息表)
- financial_records (财务记录表)
- game_settings (游戏设置表)

## 4. 启动应用

### 4.1 运行应用

在项目根目录下运行以下命令启动应用：

```bash
python app.py
```

### 4.2 访问应用

打开浏览器并访问以下地址：

```
http://127.0.0.1:5000
```

如果端口5000被占用，系统会自动使用其他可用端口。

## 5. 功能验证

### 5.1 基础功能验证

1. 首页正常加载并显示酒店基本信息
2. 时间自动推进功能正常（每2秒推进一天）
3. 酒店名称可修改
4. 时间可暂停和恢复

### 5.2 核心业务功能验证

1. 员工招聘和解雇功能正常
2. 部门解锁功能正常
3. 房间解锁和添加功能正常
4. 酒店升级功能正常
5. 营销活动和广告投放功能正常
6. 财务记录显示正常
7. 月末财务报告功能正常（每月最后一天会自动弹出财务报告）

### 5.3 数据持久化验证

1. 重启应用后数据不丢失
2. 财务记录正确保存
3. 员工信息正确保存
4. 部门解锁状态正确保存

## 6. 常见问题处理

### 6.1 端口被占用

如果提示端口被占用，可以修改app.py中的端口配置：

```python
if __name__ == '__main__':
    # 修改端口号
    app.run(debug=True, host='0.0.0.0', port=5001)
```

### 6.2 依赖安装失败

如果依赖安装失败，可以尝试以下方法：

1. 升级pip:
   ```bash
   pip install --upgrade pip
   ```

2. 使用国内镜像源安装:
   ```bash
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

### 6.3 数据库初始化失败

如果数据库初始化失败，可以尝试手动删除已存在的数据库文件并重新初始化：

```bash
# 删除数据库文件
rm hotel.db
# 或在Windows下
del hotel.db

# 重新初始化
python init_db.py
```

## 7. 生产环境部署（可选）

### 7.1 使用生产级服务器

在生产环境中，建议使用Gunicorn或uWSGI等生产级WSGI服务器：

```bash
# 安装Gunicorn
pip install gunicorn

# 使用Gunicorn运行应用
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 7.2 配置反向代理

建议使用Nginx作为反向代理服务器，配置示例：

```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 7.3 设置开机自启

在Linux系统中，可以使用systemd设置应用开机自启：

```ini
# /etc/systemd/system/hotel-management.service
[Unit]
Description=Hotel Management System
After=network.target

[Service]
User=www-data
WorkingDirectory=/path/to/hotel-management-system
ExecStart=/path/to/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
sudo systemctl enable hotel-management
sudo systemctl start hotel-management
```