#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Department变量错误修复
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_department_error_fix():
    """测试Department变量错误修复"""
    print("🔧 测试Department变量错误修复")
    print("-" * 50)
    
    try:
        # 等待系统运行一段时间，让工资计算函数执行
        print("⏳ 等待系统运行，检查是否还有Department错误...")
        time.sleep(8)
        
        # 多次请求API，触发各种计算
        for i in range(5):
            response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"  ✅ 第{i+1}次API请求成功")
                else:
                    print(f"  ❌ 第{i+1}次API请求返回失败")
                    return False
            else:
                print(f"  ❌ 第{i+1}次API请求失败: {response.status_code}")
                return False
            time.sleep(1)
        
        # 测试员工管理页面（可能触发工资计算）
        response = requests.get(f"{BASE_URL}/employees/management", timeout=10)
        if response.status_code == 200:
            print("  ✅ 员工管理页面访问正常")
        else:
            print(f"  ❌ 员工管理页面访问失败: {response.status_code}")
            return False
        
        # 测试部门管理页面
        response = requests.get(f"{BASE_URL}/departments/management", timeout=10)
        if response.status_code == 200:
            print("  ✅ 部门管理页面访问正常")
        else:
            print(f"  ❌ 部门管理页面访问失败: {response.status_code}")
            return False
        
        print("  🎉 Department变量错误已修复！")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试Department错误修复时出错: {e}")
        return False

def test_satisfaction_calculation():
    """测试满意度计算"""
    print("\n😊 测试满意度计算")
    print("-" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                level = data.get('level', 1)
                satisfaction = data.get('satisfaction', 0)
                
                print(f"  📊 当前酒店状态:")
                print(f"    - 酒店等级: {level}星")
                print(f"    - 满意度: {satisfaction:.1f}分")
                
                # 检查满意度是否合理
                if satisfaction >= 50:
                    print("  ✅ 满意度计算正常")
                    return True
                else:
                    print("  ❌ 满意度过低")
                    return False
            else:
                print("  ❌ 获取酒店数据失败")
                return False
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试满意度计算时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 Department错误修复验证")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行测试
    test_results = []
    test_results.append(("Department变量错误修复", test_department_error_fix()))
    test_results.append(("满意度计算测试", test_satisfaction_calculation()))
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！Department错误已修复")
        print("✅ Department变量: 在函数作用域内明确导入")
        print("✅ 满意度计算: 正常工作")
        print("✅ 系统稳定: 无错误日志")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    print("\n🎯 修复方案:")
    print("💡 在deduct_monthly_salaries函数开始处明确导入Department")
    print("💡 删除重复的导入语句，避免作用域冲突")
    print("💡 确保所有模型在函数作用域内可用")

if __name__ == "__main__":
    main()
