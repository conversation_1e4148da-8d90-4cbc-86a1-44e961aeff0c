#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试声望等级制和动态刷新频率修复
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_reputation_level_system():
    """测试声望等级制系统"""
    print("🏆 测试声望等级制系统")
    print("-" * 40)
    
    try:
        # 获取当前酒店状态
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                satisfaction = data.get('satisfaction', 0)
                reputation = data.get('reputation', 0)
                money = data.get('money', 0)
                
                print(f"  📊 当前状态:")
                print(f"    - 满意度: {satisfaction:.1f}分")
                print(f"    - 声望值: {reputation}")
                print(f"    - 资金: ¥{money:,}")
                
                # 分析满意度等级
                def get_satisfaction_level(satisfaction):
                    if satisfaction >= 90:
                        return '完美', 12
                    elif satisfaction >= 80:
                        return '卓越', 8
                    elif satisfaction >= 70:
                        return '优秀', 5
                    elif satisfaction >= 60:
                        return '良好', 2
                    elif satisfaction >= 50:
                        return '一般', 0
                    elif satisfaction >= 40:
                        return '较差', -1
                    elif satisfaction >= 30:
                        return '很差', -3
                    else:
                        return '极差', -5
                
                satisfaction_level, satisfaction_reputation = get_satisfaction_level(satisfaction)
                print(f"  😊 满意度等级: {satisfaction_level} (声望影响: {satisfaction_reputation:+d})")
                
                # 推进一天时间测试声望变化
                print(f"  ⏰ 推进一天时间测试声望变化...")
                advance_response = requests.post(f"{BASE_URL}/api/advance_time", timeout=10)
                
                if advance_response.status_code == 200:
                    advance_data = advance_response.json()
                    if advance_data.get('success'):
                        # 获取推进后的状态
                        after_response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
                        if after_response.status_code == 200:
                            after_data = after_response.json()
                            if after_data.get('success'):
                                new_reputation = after_data.get('reputation', 0)
                                reputation_change = new_reputation - reputation
                                
                                print(f"  📈 声望变化: {reputation} → {new_reputation} ({reputation_change:+d})")
                                
                                if reputation_change != 0:
                                    print(f"  ✅ 声望等级制系统正常工作")
                                    
                                    # 分析声望变化是否合理
                                    if satisfaction >= 60 and reputation_change > 0:
                                        print(f"  ✅ 满意度{satisfaction:.1f}分 >= 60分，声望正确增加")
                                    elif satisfaction < 60 and reputation_change <= 0:
                                        print(f"  ✅ 满意度{satisfaction:.1f}分 < 60分，声望未增加或下降")
                                    else:
                                        print(f"  ⚠️ 声望变化可能不符合预期")
                                else:
                                    print(f"  ⚠️ 声望无变化，可能需要更多时间观察")
                                    
                                return True
                            else:
                                print(f"  ❌ 获取推进后状态失败: {after_data.get('message', '')}")
                        else:
                            print(f"  ❌ 推进后状态请求失败: {after_response.status_code}")
                    else:
                        print(f"  ❌ 时间推进失败: {advance_data.get('message', '')}")
                else:
                    print(f"  ❌ 时间推进请求失败: {advance_response.status_code}")
                    
            else:
                print(f"  ❌ 获取酒店状态失败: {data.get('message', '')}")
        else:
            print(f"  ❌ 酒店状态请求失败: {response.status_code}")
            
        return False
    except Exception as e:
        print(f"  ❌ 测试声望等级制时出错: {e}")
        return False

def test_dynamic_refresh_rate():
    """测试动态刷新频率"""
    print("\n🔄 测试动态刷新频率")
    print("-" * 40)
    
    try:
        # 访问首页检查JavaScript代码
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 首页访问正常")
            
            # 检查动态刷新相关代码
            dynamic_refresh_elements = [
                "startDynamicRefresh",
                "calculateRefreshRate", 
                "updateRefreshRate",
                "baseRefreshRate",
                "minRefreshRate",
                "maxRefreshRate"
            ]
            
            found_elements = 0
            for element in dynamic_refresh_elements:
                if element in content:
                    found_elements += 1
                    print(f"    ✅ 包含 {element}")
                else:
                    print(f"    ❌ 缺少 {element}")
            
            print(f"  📊 动态刷新功能: {found_elements}/{len(dynamic_refresh_elements)}")
            
            # 检查时间倍速相关代码
            if "gameState.timeSpeed" in content:
                print("  ✅ 包含时间倍速状态管理")
            else:
                print("  ❌ 缺少时间倍速状态管理")
                
            # 检查刷新频率计算公式
            if "baseRefreshRate / gameState.timeSpeed" in content:
                print("  ✅ 包含刷新频率计算公式")
            else:
                print("  ❌ 缺少刷新频率计算公式")
                
            if found_elements >= 4:
                print("  ✅ 动态刷新频率功能基本完整")
                return True
            else:
                print("  ❌ 动态刷新频率功能不完整")
                return False
                
        else:
            print(f"  ❌ 首页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试动态刷新频率时出错: {e}")
        return False

def test_time_speed_integration():
    """测试时间倍速集成"""
    print("\n⚡ 测试时间倍速集成")
    print("-" * 40)
    
    try:
        # 获取当前时间倍速
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                current_speed = data.get('time_speed', 1)
                print(f"  📊 当前时间倍速: {current_speed}x")
                
                # 切换时间倍速
                print(f"  🔄 切换时间倍速...")
                toggle_response = requests.post(f"{BASE_URL}/api/toggle_time_speed", timeout=10)
                
                if toggle_response.status_code == 200:
                    toggle_data = toggle_response.json()
                    if toggle_data.get('success'):
                        new_speed = toggle_data.get('time_speed', 1)
                        print(f"  📈 时间倍速变化: {current_speed}x → {new_speed}x")
                        
                        if new_speed != current_speed:
                            print(f"  ✅ 时间倍速切换成功")
                            
                            # 检查响应是否包含time_speed字段
                            if 'time_speed' in toggle_data:
                                print(f"  ✅ API返回包含time_speed字段")
                            else:
                                print(f"  ❌ API返回缺少time_speed字段")
                                
                            return True
                        else:
                            print(f"  ⚠️ 时间倍速未发生变化")
                            return False
                    else:
                        print(f"  ❌ 时间倍速切换失败: {toggle_data.get('message', '')}")
                        return False
                else:
                    print(f"  ❌ 时间倍速切换请求失败: {toggle_response.status_code}")
                    return False
            else:
                print(f"  ❌ 获取酒店信息失败: {data.get('message', '')}")
                return False
        else:
            print(f"  ❌ 酒店信息请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试时间倍速集成时出错: {e}")
        return False

def test_formula_documentation():
    """测试公式文档更新"""
    print("\n📖 测试公式文档更新")
    print("-" * 40)
    
    try:
        import os
        doc_path = "INCOME_EXPENSE_FORMULAS.md"
        if os.path.exists(doc_path):
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查满意度等级制
            satisfaction_elements = [
                "满意度等级制优化版",
                "get_satisfaction_level",
                "完美 (90-100分): +12声望",
                "良好 (60-69分): +2声望"
            ]
            
            found_satisfaction = sum(1 for element in satisfaction_elements if element in content)
            print(f"  😊 满意度等级制文档: {found_satisfaction}/{len(satisfaction_elements)}")
            
            # 检查财务表现等级制
            financial_elements = [
                "财务表现等级制优化版",
                "get_financial_performance_level", 
                "超盈 (>1,000,000): +15声望",
                "严重亏损 (<-500,000): -8声望"
            ]
            
            found_financial = sum(1 for element in financial_elements if element in content)
            print(f"  💰 财务表现等级制文档: {found_financial}/{len(financial_elements)}")
            
            if found_satisfaction >= 3 and found_financial >= 3:
                print(f"  ✅ 公式文档更新完整")
                return True
            else:
                print(f"  ❌ 公式文档更新不完整")
                return False
        else:
            print(f"  ❌ 公式文档不存在")
            return False
    except Exception as e:
        print(f"  ❌ 测试公式文档时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 声望等级制和动态刷新频率修复验证")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项测试
    test_results = []
    test_results.append(("声望等级制系统", test_reputation_level_system()))
    test_results.append(("动态刷新频率", test_dynamic_refresh_rate()))
    test_results.append(("时间倍速集成", test_time_speed_integration()))
    test_results.append(("公式文档更新", test_formula_documentation()))
    
    print("\n" + "=" * 60)
    print("📋 修复验证总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！声望和刷新频率已优化")
        print("✅ 声望等级制: 基于满意度和财务表现等级")
        print("✅ 动态刷新频率: 根据时间倍速自动调整")
        print("✅ 时间倍速集成: 切换倍速时更新刷新频率")
        print("✅ 公式文档完整: 详细记录等级制计算方式")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
    
    print("\n🎯 核心改进:")
    print("💡 满意度等级制: 60分以上获得正向声望，最高12点")
    print("💡 财务表现等级制: 11个等级，最高15点声望奖励")
    print("💡 动态刷新频率: 倍速越高，刷新越快，最快500ms")
    print("💡 用户体验优化: 倍速时不再感觉刷新缓慢")
    
    print("\n🚀 可以通过浏览器访问 http://127.0.0.1:5000 查看效果")

if __name__ == "__main__":
    main()
