# 🧮 酒店管理系统计算公式完整指南

## 📊 核心数值计算公式

### 1. 入住率计算公式

**基础公式**：
```
最终入住率 = 基础入住率 + 酒店等级加成 + 满意度影响 + 声望影响 + 营销影响 + 季节影响 + 周末加成 + 随机波动
```

**详细参数**：

**基础入住率（按房间类型）**：
- 单人间：75%
- 标准间：80%
- 大床房：78%
- 家庭房：72%
- 商务间：85%
- 行政间：88%
- 豪华间：82%
- 总统套房：75%
- 皇家套房：70%
- 总统别墅：65%
- 皇宫套房：60%

**影响因素**：
- **酒店等级加成**：(酒店等级 - 1) × 2%
- **满意度影响**：(满意度 - 50) × 0.1%
- **声望影响**：声望等级 × 1%
- **营销影响**：所有活跃营销活动效果累加
- **季节影响**：
  - 1月：-5%，2月：-3%，3月：0%，4月：+2%
  - 5月：+5%，6月：+8%，7月：+10%，8月：+8%
  - 9月：+3%，10月：0%，11月：-2%，12月：+2%
- **周末加成**：周六日 +5%
- **随机波动**：±2%

**入住率范围限制**：20% ≤ 入住率 ≤ 95%

### 2. 房间收入计算公式

**主要收入公式**：
```
房间收入 = Σ(房间数量 × 房间单价 × 入住率)
```

**附加收入**：
- **服务收入** = 房间收入 × 10%
- **其他收入** = 房间收入 × 5%

**总收入公式**：
```
总收入 = 房间收入 × 1.15
```

### 3. 部门繁忙度计算公式 ⭐

**核心公式**：
```
部门繁忙度 = min(200%, (估算客户数 / 部门总服务能力) × 100%)
```

**估算客户数计算**：
```
估算客户数 = Σ(房间数量 × 入住率 × 每房客人数)
```

**每房客人数标准**：
- 所有房型：1人/房（简化计算）

**部门服务能力计算**：
```
部门总服务能力 = max(1, Σ(基础服务能力 × 员工等级系数))
```

**基础服务能力（每人每天服务客户数）**：
- 前台部：15人（需要更多人手）
- 客房部：10人（需要更多人手）
- 餐饮部：25人
- 营销部：40人
- 安保部：50人
- 财务部：60人（高级部门）
- 商务部：80人（高级部门）
- 康养部：70人（高级部门）
- 工程部：100人（高级部门）
- 人事部：50人
- 董事会：150人（最高级部门）

**员工等级服务能力系数**：
- 初级员工：1.0倍
- 中级员工：1.5倍
- 高级员工：2.0倍
- 特级员工：3.0倍

**繁忙度影响**：
- < 50%：满意度 +10
- 50-80%：满意度 +0
- 80-100%：满意度 -5
- > 100%：满意度 -15

### 4. 员工工资计算公式

**实际工资公式**：
```
员工实际工资 = 基础工资 × (1 + 工龄 × 20%)
```

**基础工资标准**：
- 初级员工：¥3,000/月
- 中级员工：¥5,000/月
- 高级员工：¥8,000/月
- 特级员工：¥15,000/月

**工龄计算**：
```
工龄 = (当前游戏日期 - 员工入职日期) / 365天
工龄上限：10年
```

**日工资支出**：
```
日工资支出 = Σ(员工实际工资) / 30天
```

### 5. 满意度计算公式

**基础满意度**：
```
满意度 = 50分（基础） + 酒店等级×2分 + 入住率影响 + 部门繁忙度影响 + 其他加成
```

**入住率对满意度影响**：
- 入住率 ≥ 90%：+15分
- 入住率 ≥ 80%：+10分
- 入住率 ≥ 70%：+5分
- 入住率 ≥ 60%：+0分
- 入住率 ≥ 50%：-5分
- 入住率 < 50%：-10分

**部门繁忙度影响**：
- 平均繁忙度 < 50%：+10分
- 平均繁忙度 50-80%：+0分
- 平均繁忙度 80-100%：-5分
- 平均繁忙度 > 100%：-15分

**其他影响因素**：
- **员工与房间比例**：充足员工提升满意度
- **房间等级加成**：高级房型提供额外加成
- **部门特殊效果**：已解锁部门提供加成
- **随机事件**：可能增减满意度

**满意度范围**：0-100分

### 6. 声望值计算公式

**每日声望变化**：
```
声望变化 = 满意度影响 + 财务表现影响 + 特殊事件影响
```

**满意度对声望影响**：
- 极差(0-29分)：-5声望/天
- 很差(30-39分)：-3声望/天
- 较差(40-49分)：-1声望/天
- 一般(50-59分)：0声望/天
- 良好(60-69分)：+2声望/天
- 优秀(70-79分)：+5声望/天
- 卓越(80-89分)：+8声望/天
- 完美(90-100分)：+12声望/天

**财务表现影响**：
- 亏损：-2声望/天
- 微利(0-1万)：0声望/天
- 盈利(1-5万)：+1声望/天
- 良好(5-10万)：+2声望/天
- 优秀(>10万)：+3声望/天

### 7. 支出计算公式

**房间维护费**：
```
日维护费 = Σ(房间数量 × 房间价格 × 20%)
```

**房间价格标准**：
- 单人间：¥300，标准间：¥500，大床房：¥600，家庭房：¥800
- 商务间：¥1000，行政间：¥1200，豪华间：¥1500，总统套房：¥2000
- 皇家套房：¥3000，总统别墅：¥5000，皇宫套房：¥8000

**水电费**：
```
日水电费 = 房间总数 × 5元/间/日
```

**营销费用**：
```
日营销费 = 日收入 × 2%
```

**其他支出**：
```
日其他支出 = 日收入 × 1%
```

### 8. 月度费用计算

**月初扣除项目**：

**员工工资**：
```
月工资总额 = Σ(员工实际工资)
```

**房间维护费**：
```
月维护费 = 房间总数 × 100元/间/月
```

**部门优化效果**：
- **工程部**：维护费用减少50%
- **财务部**：所有支出减少5%

### 9. 随机事件触发机制

**触发概率**：
- 基础概率：2%/天（确保每月平均约2个事件）
- 强制触发条件：
  - 本月事件数 < 2 且剩余天数 ≤ 5天
  - 本月事件数 = 0 且剩余天数 ≤ 10天
- **避免月初触发**：不在每月1号触发，防止与月度报告冲突

**事件类型分布**：
- 正面事件：40%
- 负面事件：40%
- 中性事件：20%

**防重复机制**：
- 每个事件只向玩家显示一次
- 事件显示后自动标记为已显示
- 前端检查时只返回未显示的新事件

### 10. 酒店升级条件

**升级要求公式**：
```
升级条件 = 资金要求 AND 声望要求 AND 运营天数要求 AND 满意度要求
```

**具体要求**（示例）：
- 2星：资金100万 + 声望500 + 运营30天 + 满意度60分
- 3星：资金500万 + 声望2000 + 运营90天 + 满意度65分
- 4星：资金1500万 + 声望5000 + 运营180天 + 满意度70分

## 🎯 调整建议

### 平衡性调整参数

**如需调整游戏难度，可修改以下关键参数**：

1. **入住率基础值**：调整各房型基础入住率
2. **满意度影响系数**：调整0.1%的影响系数
3. **声望影响系数**：调整1%的影响系数
4. **部门服务能力**：调整各部门基础服务能力
5. **员工等级系数**：调整1.0-3.0的服务能力倍数
6. **随机事件概率**：调整8%的基础触发概率

### 关键文件位置

- **入住率计算**：`app/main/utils.py` - `calculate_stable_occupancy_rate()`
- **部门繁忙度**：`app/main/utils.py` - `calculate_department_busy_level()`
- **满意度计算**：`app/main/utils.py` - `calculate_satisfaction_and_reputation()`
- **财务计算**：`app/main/utils.py` - `calculate_daily_finances()`
- **随机事件**：`app/main/random_events.py` - `trigger_random_event()`

## 11. 员工相关计算公式

### 员工满意度计算
**公式**：
```
员工满意度 = 基础满意度 + 工龄加成 + 工资加成
```

**详细计算**：
- **基础满意度**：70分
- **工龄加成**：min(20, 工龄年数 × 2分)，最多+20分
- **工资加成**：min(15, (实际工资/基础工资 - 1) × 30分)，最多+15分

**员工平均满意度**：
```
平均满意度 = Σ(员工满意度) / 员工总数
```

### 员工升级费用
**公式**：
```
升级费用 = 当前工资 × 50%
升级后工资 = 当前工资 × 120%
```

**等级升级路径**：
- 初级 → 中级 → 高级 → 特级

### 员工解雇费用
**公式**：
```
遣散费 = 实际工资 × (工龄年数 + 1)
```

### 员工招聘费用
**按等级收费**：
- 初级员工：¥10,000
- 中级员工：¥30,000
- 高级员工：¥80,000
- 特级员工：¥200,000

## 12. 营销活动计算公式

### 品牌知名度计算
**公式**：
```
品牌知名度 = min(100%, 基础知名度 + 声望加成 + 营销加成 + 等级加成)
```

**详细参数**：
- **基础知名度**：20%
- **声望加成**：min(30%, 声望等级 × 3%)
- **营销加成**：min(40%, 营销效果 × 0.5%)
- **等级加成**：酒店等级 × 2%

### 营销活动效果
**广告效果计算**：
```
广告效果 = 基础效果 + 等级加成
基础效果 = 5%
等级加成 = (酒店等级 - 1) × 2%
```

## 13. 客户满意度详细计算

### 员工与房间比例影响
**公式**：
```
员工比例 = 员工总数 / 房间总数
```

**影响效果**：
- 比例 ≥ 0.5（每2间房1员工）：满意度 +8分
- 比例 ≥ 0.3（每3间房1员工）：满意度 +5分
- 比例 ≥ 0.2（每5间房1员工）：满意度 +2分
- 比例 < 0.2：满意度 -5分

### 房间等级加成
**高级房型满意度加成**：
- 皇宫套房：每间 +10分
- 总统别墅：每间 +8分
- 皇家套房：每间 +6分
- 总统套房：每间 +4分
- 豪华间：每间 +2分
- 其他房型：每间 +0分

### 部门特殊效果
**已解锁部门加成**：
- 康养部：满意度 +5分
- 娱乐部：满意度 +3分
- 会议部：满意度 +2分

## 14. 声望等级系统

### 声望等级划分
**声望值对应等级**：
- 0-499：默默无闻
- 500-1999：小有名气
- 2000-4999：知名酒店
- 5000-9999：著名品牌
- 10000-19999：行业标杆
- 20000-49999：行业领袖
- 50000-99999：国际知名
- 100000+：传奇酒店

### 声望等级计算
**公式**：
```
声望等级 = min(10, 声望值 // 1000)
```

## 15. 月度财务报告系统

### 报告触发时间
- **触发时间**：每月1号自动生成上个月的财务报告
- **显示方式**：弹窗显示详细报告 + 首页汇总表格

### 收入分类
- **房间收入**：各房型收入
- **服务收入**：附加服务收入
- **其他收入**：杂项收入

### 支出分类
- **员工工资**：人力成本
- **房间维护费**：设施维护
- **水电费**：公用事业
- **营销费用**：推广成本
- **其他支出**：杂项费用
- **部门运营费**：各部门运营成本

### 首页财务汇总表
- **显示范围**：最近12个月的月度汇总
- **数据内容**：总收入、总支出、净利润
- **月份格式**：YYYY-MM 格式（如：2025-05）
- **排序方式**：按时间倒序显示（最新月份在前）
- **视觉效果**：Excel式表格，当月数据高亮显示
- **更新频率**：实时更新，与游戏数据同步

## 🎯 平衡性调整指南

### 关键平衡点
1. **入住率平衡**：基础入住率决定收入基础
2. **满意度平衡**：影响声望增长和入住率
3. **繁忙度平衡**：员工数量与客户数量的平衡
4. **成本平衡**：收入与支出的合理比例

### 建议调整参数
- **降低难度**：提高基础入住率、降低员工招聘费用
- **提高难度**：降低基础入住率、增加维护费用
- **平衡收支**：调整日常支出比例（营销费、其他支出）

所有计算公式均已实现并在系统中正常运行。玩家可根据实际游戏体验调整相关参数以获得最佳平衡性。
