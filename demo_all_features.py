#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
酒店管理系统功能演示脚本
展示所有功能的完整使用流程
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def api_call(endpoint, method="GET", data=None):
    """API调用封装"""
    try:
        url = f"{BASE_URL}{endpoint}"
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            return True, response.json() if response.content else {}
        else:
            return False, f"HTTP {response.status_code}: {response.text}"
    except Exception as e:
        return False, str(e)

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print('='*60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")

def print_result(success, message):
    """打印结果"""
    symbol = "✅" if success else "❌"
    print(f"   {symbol} {message}")

def demo_hotel_info():
    """演示酒店基本信息功能"""
    print_section("酒店基本信息管理")
    
    print_step(1, "获取酒店基本信息")
    success, data = api_call("/api/hotel_info")
    if success:
        hotel = data
        print_result(True, f"酒店名称: {hotel['hotel_name']}")
        print_result(True, f"酒店等级: {hotel['level']}星")
        print_result(True, f"当前资金: ¥{hotel['money']:,}")
        print_result(True, f"当前日期: {hotel['current_date']}")
        print_result(True, f"运营天数: {hotel['days_elapsed']}天")
        print_result(True, f"客户满意度: {hotel['satisfaction']}")
        print_result(True, f"声望值: {hotel['reputation']}")
    else:
        print_result(False, f"获取酒店信息失败: {data}")

def demo_time_system():
    """演示时间系统功能"""
    print_section("时间系统管理")
    
    print_step(1, "暂停/恢复时间")
    success, data = api_call("/api/toggle_time", "POST")
    if success:
        print_result(True, f"时间状态切换成功: {data.get('message', '')}")
    else:
        print_result(False, f"时间控制失败: {data}")
    
    print_step(2, "切换时间速度")
    success, data = api_call("/api/toggle_time_speed", "POST")
    if success:
        print_result(True, f"时间速度切换成功: {data.get('message', '')}")
    else:
        print_result(False, f"时间速度控制失败: {data}")

def demo_employee_management():
    """演示员工管理功能"""
    print_section("员工管理系统")
    
    print_step(1, "查看候选人列表")
    success, data = api_call("/employees/get_candidates_list")
    if success:
        candidates = data.get("candidates", [])
        print_result(True, f"获取到 {len(candidates)} 名候选人")
        for i, candidate in enumerate(candidates[:3]):  # 显示前3个
            print(f"      候选人 {i+1}: {candidate['name']} - {candidate['department']} - {candidate['level']}")
    else:
        print_result(False, f"获取候选人失败: {data}")
        return
    
    if candidates:
        print_step(2, "招聘员工")
        hire_data = {"candidate_id": candidates[0]["id"]}
        success, data = api_call("/employees/hire", "POST", hire_data)
        if success:
            print_result(True, f"招聘成功: {data.get('message', '')}")
        else:
            print_result(False, f"招聘失败: {data}")

def demo_department_management():
    """演示部门管理功能"""
    print_section("部门管理系统")
    
    print_step(1, "查看部门列表")
    success, data = api_call("/departments/get_departments_list")
    if success:
        departments = data.get("departments", [])
        print_result(True, f"获取到 {len(departments)} 个部门")
        
        unlocked_count = sum(1 for dept in departments if dept['is_unlocked'])
        print_result(True, f"已解锁部门: {unlocked_count} 个")
        
        # 显示未解锁的部门
        locked_departments = [dept for dept in departments if not dept['is_unlocked']]
        if locked_departments:
            print("      未解锁部门:")
            for dept in locked_departments[:3]:  # 显示前3个
                print(f"        - {dept['name']}: 需要{dept['required_level']}星, 费用¥{dept['unlock_cost']:,}")
    else:
        print_result(False, f"获取部门列表失败: {data}")

def demo_room_management():
    """演示房间管理功能"""
    print_section("房间管理系统")
    
    print_step(1, "查看房间列表")
    success, data = api_call("/rooms/get_rooms_list")
    if success:
        rooms = data.get("rooms", [])
        print_result(True, f"获取到 {len(rooms)} 种房间类型")
        
        for room in rooms:
            print(f"      {room['type']}: {room['count']}间, ¥{room['price']}/晚, 入住率{room['occupancy_rate']:.1f}%")
    else:
        print_result(False, f"获取房间列表失败: {data}")
        return
    
    print_step(2, "建设房间")
    build_data = {"room_type": "单人间", "quantity": 2}
    success, data = api_call("/rooms/build", "POST", build_data)
    if success:
        print_result(True, f"房间建设成功: {data.get('message', '')}")
    else:
        print_result(False, f"房间建设失败: {data}")
    
    print_step(3, "设置房间价格")
    price_data = {"room_type": "单人间", "price": 350}
    success, data = api_call("/rooms/set_price", "POST", price_data)
    if success:
        print_result(True, f"价格设置成功: {data.get('message', '')}")
    else:
        print_result(False, f"价格设置失败: {data}")

def demo_marketing_system():
    """演示营销管理功能"""
    print_section("营销管理系统")
    
    print_step(1, "查看营销活动")
    success, data = api_call("/marketing/get_campaigns_list")
    if success:
        campaigns = data.get("campaigns", [])
        print_result(True, f"获取到 {len(campaigns)} 个营销活动")
        
        active_campaigns = [c for c in campaigns if c.get('is_active')]
        print_result(True, f"活跃营销活动: {len(active_campaigns)} 个")
    else:
        print_result(False, f"获取营销活动失败: {data}")

def demo_finance_system():
    """演示财务管理功能"""
    print_section("财务管理系统")
    
    print_step(1, "查看财务数据")
    success, data = api_call("/finance/get_financial_data")
    if success:
        records = data.get("records", [])
        print_result(True, f"获取到 {len(records)} 条财务记录")
        
        summary = data.get("summary", {})
        if summary:
            print(f"      今日收入: ¥{summary.get('daily_income', 0):,.2f}")
            print(f"      今日支出: ¥{summary.get('daily_expense', 0):,.2f}")
            print(f"      今日利润: ¥{summary.get('daily_profit', 0):,.2f}")
    else:
        print_result(False, f"获取财务数据失败: {data}")

def demo_achievement_system():
    """演示成就系统功能"""
    print_section("成就系统")
    
    print_step(1, "查看成就列表")
    success, data = api_call("/achievements/get_achievements_list")
    if success:
        achievements = data.get("achievements", {})
        statistics = data.get("statistics", {})
        
        total = statistics.get("total", 0)
        achieved = statistics.get("achieved", 0)
        rate = statistics.get("rate", 0)
        
        print_result(True, f"成就总数: {total}")
        print_result(True, f"已完成: {achieved}")
        print_result(True, f"完成率: {rate}%")
        
        # 显示各类别成就数量
        for category, category_achievements in achievements.items():
            if category_achievements:
                achieved_in_category = sum(1 for a in category_achievements if a['is_achieved'])
                print(f"      {category}: {achieved_in_category}/{len(category_achievements)}")
    else:
        print_result(False, f"获取成就列表失败: {data}")

def demo_save_system():
    """演示存档系统功能"""
    print_section("存档系统")
    
    print_step(1, "查看存档槽")
    success, data = api_call("/api/get_save_slots")
    if success:
        slots = data.get("slots", {})
        print_result(True, f"存档槽状态:")
        for slot_name, slot_data in slots.items():
            if slot_data:
                print(f"      {slot_name}: {slot_data['hotel_name']} - {slot_data['date']} - {slot_data['level']}星")
            else:
                print(f"      {slot_name}: 空")
    else:
        print_result(False, f"获取存档槽失败: {data}")
    
    print_step(2, "保存游戏")
    save_data = {"slot": 1}
    success, data = api_call("/api/save_to_slot", "POST", save_data)
    if success:
        print_result(True, f"保存成功: {data.get('message', '')}")
    else:
        print_result(False, f"保存失败: {data}")

def main():
    """主演示函数"""
    print("🎮 酒店管理系统功能演示")
    print(f"演示目标: {BASE_URL}")
    print("="*60)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 演示所有功能
    demo_hotel_info()
    demo_time_system()
    demo_employee_management()
    demo_department_management()
    demo_room_management()
    demo_marketing_system()
    demo_finance_system()
    demo_achievement_system()
    demo_save_system()
    
    print_section("演示完成")
    print("🎉 所有功能演示完成！")
    print("💡 您可以通过浏览器访问 http://127.0.0.1:5000 来体验完整的用户界面")

if __name__ == "__main__":
    main()
