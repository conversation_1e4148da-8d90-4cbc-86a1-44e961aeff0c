import sqlite3
import os
from app import create_app
from collections import Counter

def check_achievements():
    """检查成就数据"""
    app = create_app()
    
    with app.app_context():
        # 获取数据库路径
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        print(f"数据库路径: {db_path}")
        
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 查询所有成就
            cursor.execute("SELECT id, hotel_id, name, description, is_unlocked FROM achievement")
            achievements = cursor.fetchall()
            
            print(f"总共找到 {len(achievements)} 个成就记录")
            
            # 统计每个成就名称出现的次数
            name_counts = Counter([a[2] for a in achievements])
            
            # 显示重复的成就
            duplicates = {name: count for name, count in name_counts.items() if count > 1}
            if duplicates:
                print("\n重复的成就:")
                for name, count in duplicates.items():
                    print(f"  {name}: {count} 次")
            else:
                print("\n没有发现重复的成就")
            
            # 显示每个分类的成就数量
            categories = {
                '财务成就': ['初次盈利', '百万富翁', '千万富翁', '亿万富翁', '月入百万', 
                           '月入千万', '年度盈利王', '连续盈利', '财务大师', '投资专家'],
                '员工成就': ['首位员工', '百人团队', '千人团队', '万人团队', '人才伯乐',
                           '培训大师', '高薪一族', '人事专家', '员工满意', '团队建设'],
                '发展成就': ['星光初现', '三星荣耀', '四海为家', '五星级别', '六六大顺',
                           '七星高照', '八方来客', '九霄云外', '部门齐全', '房间帝国'],
                '经营成就': ['客满为患', '满意服务', '声望卓著', '营销专家', '广告大王',
                           '好评如潮', '生意兴隆', '稳定发展', '高端客户', '品牌价值'],
                '特殊成就': ['时间管理大师', '存档专家', '探索者', '完美主义者', '长期经营',
                           '快速发展', '节约大师', '平衡大师', '幸运之星', '挑战者']
            }
            
            print("\n各分类成就数量:")
            for category, names in categories.items():
                count = sum(name_counts.get(name, 0) for name in names)
                print(f"  {category}: {count} 个")
                
        except Exception as e:
            print(f"检查过程中出现错误: {e}")
        finally:
            conn.close()

if __name__ == '__main__':
    check_achievements()