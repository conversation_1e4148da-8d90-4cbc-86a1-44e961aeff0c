#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试模块导入
"""

try:
    print("测试导入存档模块...")
    from app.save import bp as save_bp
    print(f"✅ 存档蓝图导入成功: {save_bp}")
    print(f"蓝图名称: {save_bp.name}")
    print(f"URL前缀: {save_bp.url_prefix}")
    
    # 列出所有路由
    print("存档蓝图路由:")
    for rule in save_bp.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint}")
        
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
