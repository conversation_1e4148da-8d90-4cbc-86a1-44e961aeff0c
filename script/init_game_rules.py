#!/usr/bin/env python
"""
初始化游戏规则脚本
此脚本用于在数据库中设置完整的游戏规则，基于需求文档
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app import create_app
from app.models import GameSetting
import json

def init_game_rules():
    """初始化所有游戏规则"""
    # 创建应用实例以获取数据库连接
    app = create_app()
    
    with app.app_context():
        # 定义游戏规则（基于需求文档）
        game_rules = {
            # 部门解锁规则 (基于需求文档3.2节)
            "department_unlock_rules": {
                "前台部": {"level": 1, "cost": 0},
                "客房部": {"level": 1, "cost": 0},
                "餐饮部": {"level": 2, "cost": 1000000},
                "安保部": {"level": 3, "cost": 3000000},
                "财务部": {"level": 4, "cost": 8000000},
                "商务部": {"level": 5, "cost": 20000000},
                "营销部": {"level": 1, "cost": 0},
                "康养部": {"level": 6, "cost": 50000000}
            },
            
            # 酒店升级规则 (基于需求文档3.2节)
            "hotel_upgrade_rules": {
                "2": {"cost": 1000000},
                "3": {"cost": 3000000},
                "4": {"cost": 8000000},
                "5": {"cost": 20000000},
                "6": {"cost": 50000000}
            },
            
            # 部门活动规则 (基于需求文档3.5.3节)
            "department_activity_rules": {
                "前台": {"cost": 2000, "income": 5000},
                "客房部": {"cost": 5000, "income": 12000},
                "餐饮部": {"cost": 10000, "income": 25000},
                "安保部": {"cost": 3000, "income": 3000},
                "人事部": {"cost": 8000, "income": 8000},
                "财务部": {"cost": 1000, "income": 2000}
            },
            
            # 员工等级规则 (基于需求文档3.5.1节)
            "employee_level_rules": {
                "初级": {
                    "base_salary": 2000,
                    "salary_growth": [2200, 2600, 3000],
                    "promotion_years": 3,
                    "next_level": "中级"
                },
                "中级": {
                    "base_salary": 4000,
                    "salary_growth": [4500, 5000, 6000],
                    "promotion_years": 5,
                    "next_level": "高级"
                },
                "高级": {
                    "base_salary": 7000,
                    "salary_growth": [8000, 9000, 10000],
                    "promotion_years": 10,
                    "next_level": "特级"
                },
                "特级": {
                    "base_salary": 12000,
                    "salary_growth": [],
                    "promotion_years": 0,
                    "next_level": None
                }
            },
            
            # 员工招聘概率规则 (基于需求文档3.5.4节)
            "employee_recruitment_rules": {
                "1": {
                    "初级": {"probability": 100, "max_count": 10},
                    "中级": {"probability": 0, "max_count": 0},
                    "高级": {"probability": 0, "max_count": 0},
                    "特级": {"probability": 0, "max_count": 0}
                },
                "2": {
                    "初级": {"probability": 50, "max_count": 8},
                    "中级": {"probability": 50, "max_count": 2},
                    "高级": {"probability": 0, "max_count": 0},
                    "特级": {"probability": 0, "max_count": 0}
                },
                "3": {
                    "初级": {"probability": 70, "max_count": 7},
                    "中级": {"probability": 30, "max_count": 2},
                    "高级": {"probability": 30, "max_count": 1},
                    "特级": {"probability": 0, "max_count": 0}
                },
                "4": {
                    "初级": {"probability": 60, "max_count": 6},
                    "中级": {"probability": 40, "max_count": 2},
                    "高级": {"probability": 30, "max_count": 2},
                    "特级": {"probability": 10, "max_count": 1}
                },
                "5": {
                    "初级": {"probability": 50, "max_count": 5},
                    "中级": {"probability": 50, "max_count": 2},
                    "高级": {"probability": 40, "max_count": 2},
                    "特级": {"probability": 10, "max_count": 1}
                },
                "6": {
                    "初级": {"probability": 40, "max_count": 4},
                    "中级": {"probability": 50, "max_count": 2},
                    "高级": {"probability": 40, "max_count": 2},
                    "特级": {"probability": 20, "max_count": 2}
                }
            },
            
            # 营销活动规则 (基于需求文档3.5.3节)
            "marketing_activity_rules": {
                "初级": {
                    "profit_rate": 20,
                    "amount": 20000,
                    "min_hotel_level": 1,
                    "frequency": {
                        "1": 1,
                        "2": 1,
                        "3": 3,
                        "4": 10,
                        "5": 50,
                        "6": 50
                    }
                },
                "中级": {
                    "profit_rate": 50,
                    "amount": 200000,
                    "min_hotel_level": 3,
                    "frequency": {
                        "3": 1,
                        "4": 3,
                        "5": 10,
                        "6": 10
                    }
                },
                "高级": {
                    "profit_rate": 100,
                    "amount": 1000000,
                    "min_hotel_level": 5,
                    "frequency": {
                        "5": 1,
                        "6": 3
                    }
                },
                "特级": {
                    "profit_rate": 200,
                    "amount": 5000000,
                    "min_hotel_level": 6,
                    "frequency": {
                        "6": 1
                    }
                }
            },
            
            # 房间类型和价格 (基于需求文档3.3节)
            "room_types_and_prices": {
                "单人间": 100,
                "标准间": 200,
                "大床房": 300,
                "家庭房": 500,
                "商务间": 800,
                "行政间": 1200,
                "豪华间": 1800,
                "总统套房": 3000
            },
            
            # 房间解锁规则 (基于需求文档3.2节)
            "room_unlock_rules": {
                "1": ["单人间", "标准间"],
                "2": ["单人间", "标准间", "大床房"],
                "3": ["单人间", "标准间", "大床房", "家庭房"],
                "4": ["单人间", "标准间", "大床房", "家庭房", "商务间", "行政间"],
                "5": ["单人间", "标准间", "大床房", "家庭房", "商务间", "行政间", "豪华间"],
                "6": ["单人间", "标准间", "大床房", "家庭房", "商务间", "行政间", "豪华间", "总统套房"]
            },
            
            # 入住率规则 (基于需求文档3.6节)
            "occupancy_rate_rules": {
                "1": {
                    "单人间": [80, 100],
                    "标准间": [50, 90]
                },
                "2": {
                    "单人间": [90, 100],
                    "标准间": [70, 90],
                    "大床房": [50, 70]
                },
                "3": {
                    "单人间": [100, 100],
                    "标准间": [80, 100],
                    "大床房": [60, 90],
                    "家庭房": [60, 80]
                },
                "4": {
                    "单人间": [100, 100],
                    "标准间": [100, 100],
                    "大床房": [80, 100],
                    "家庭房": [70, 100],
                    "商务间": [50, 90],
                    "行政间": [50, 80]
                },
                "5": {
                    "单人间": [100, 100],
                    "标准间": [100, 100],
                    "大床房": [90, 100],
                    "家庭房": [80, 100],
                    "商务间": [80, 100],
                    "行政间": [70, 100],
                    "豪华间": [60, 100]
                },
                "6": {
                    "单人间": [100, 100],
                    "标准间": [100, 100],
                    "大床房": [100, 100],
                    "家庭房": [90, 100],
                    "商务间": [90, 100],
                    "行政间": [80, 100],
                    "豪华间": [70, 100],
                    "总统套房": [50, 100]
                }
            },
            
            # 员工服务能力规则 (基于需求文档3.5.2节)
            "employee_capacity_rules": {
                "前台部": {
                    "初级": 20,
                    "中级": 50,
                    "高级": 100,
                    "特级": 200
                },
                "客房部": {
                    "初级": 15,
                    "中级": 30,
                    "高级": 60,
                    "特级": 100
                },
                "餐饮部": {
                    "初级": 20,
                    "中级": 50,
                    "高级": 100,
                    "特级": 200
                },
                "安保部": {
                    "初级": 20,
                    "中级": 50,
                    "高级": 100,
                    "特级": 200
                },
                "财务部": {
                    "初级": 30,
                    "中级": 50,
                    "高级": 80,
                    "特级": 150
                },
                "商务部": {
                    "初级": 50,
                    "中级": 100,
                    "高级": 200,
                    "特级": 300
                }
            },
            
            # 康养部盈利规则 (基于需求文档3.5.3节)
            "wellness_department_rules": {
                "weight_rules": {
                    "0-300": 1,
                    "300-500": 2,
                    "500-1000": 3,
                    "1000+": 4
                },
                "base_income_per_employee": 10000
            }
        }
        
        # 将规则保存到数据库
        for key, value in game_rules.items():
            # 检查是否已存在
            setting = GameSetting.query.filter_by(key=key).first()
            if setting:
                # 更新现有设置
                setting.value = json.dumps(value, ensure_ascii=False)
                print(f"更新设置: {key}")
            else:
                # 创建新设置
                setting = GameSetting(key=key, value=json.dumps(value, ensure_ascii=False))
                from app import db
                db.session.add(setting)
                print(f"创建设置: {key}")
        
        # 提交更改
        try:
            from app import db
            db.session.commit()
            print("所有游戏规则初始化完成")
        except Exception as e:
            print(f"保存游戏规则时出错: {e}")
            from app import db
            db.session.rollback()

if __name__ == "__main__":
    init_game_rules()