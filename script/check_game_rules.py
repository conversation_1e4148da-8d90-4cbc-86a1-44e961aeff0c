#!/usr/bin/env python
"""
检查游戏规则脚本
此脚本用于检查数据库中的所有游戏规则设置
"""

import json
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app import create_app
from app.models import GameSetting

# 规则中文说明映射
rule_descriptions = {
    "department_unlock_cost_餐饮部": "餐饮部解锁费用",
    "department_unlock_cost_营销部": "营销部解锁费用",
    "department_unlock_cost_安保部": "安保部解锁费用",
    "department_unlock_cost_财务部": "财务部解锁费用",
    "department_unlock_cost_商务部": "商务部解锁费用",
    "department_unlock_cost_康养部": "康养部解锁费用",
    "room_unlock_rules": "房间解锁规则",
    "room_price_单人间": "单人间价格",
    "room_price_标准间": "标准间价格",
    "room_price_大床房": "大床房价格",
    "room_price_家庭房": "家庭房价格",
    "room_price_商务间": "商务间价格",
    "room_price_行政间": "行政间价格",
    "room_price_豪华间": "豪华间价格",
    "room_price_总统套房": "总统套房价格",
    "employee_capacity_rules": "员工服务能力规则",
    "department_unlock_rules": "部门解锁规则",
    "hotel_upgrade_rules": "酒店升级规则",
    "department_activity_rules": "部门活动规则",
    "employee_level_rules": "员工等级规则",
    "employee_recruitment_rules": "员工招聘规则",
    "marketing_activity_rules": "营销活动规则",
    "room_types_and_prices": "房间类型和价格",
    "occupancy_rate_rules": "入住率规则",
    "wellness_department_rules": "康养部规则"
}

expected_rules = [
    "department_unlock_rules",
    "hotel_upgrade_rules", 
    "department_activity_rules",
    "employee_level_rules",
    "employee_recruitment_rules",
    "marketing_activity_rules",
    "room_types_and_prices",
    "room_unlock_rules",
    "occupancy_rate_rules",
    "employee_capacity_rules",
    "wellness_department_rules"
]

def check_game_rules():
    """检查数据库中的所有游戏规则"""
    app = create_app()
    with app.app_context():
        settings = GameSetting.query.all()
        
        # 准备输出内容
        output_lines = []
        output_lines.append('数据库规则设置:')
        for setting in settings:
            # 获取中文说明，如果没有则使用原字段名
            description = rule_descriptions.get(setting.key, setting.key)
            output_lines.append(f'- {description}')
            
            # Try to parse JSON values to verify they're valid
            if setting.value:
                try:
                    parsed = json.loads(setting.value)
                    if isinstance(parsed, dict):
                        output_lines.append(f'  内容项数: {len(parsed)} 项')
                        for k in list(parsed.keys())[:5]:  # Show first 5 keys only
                            output_lines.append(f'    - {k}')
                        if len(parsed.keys()) > 5:
                            output_lines.append(f'    ... 还有 {len(parsed.keys()) - 5} 项')
                    else:
                        output_lines.append(f'  内容: {str(parsed)[:100]}{"..." if len(str(parsed)) > 100 else ""}')
                except json.JSONDecodeError:
                    output_lines.append(f'  内容 (非JSON格式): {str(setting.value)[:100]}{"..." if len(str(setting.value)) > 100 else ""}')
            else:
                output_lines.append('  内容: 空')
            output_lines.append('')
        
        output_lines.append(f'总计: {len(settings)} 个规则设置')
        
        # Check for missing expected rules
        existing_keys = [s.key for s in settings]
        missing_rules = [rule for rule in expected_rules if rule not in existing_keys]
        
        if missing_rules:
            output_lines.append('\n缺失的预期规则:')
            for rule in missing_rules:
                output_lines.append(f'- {rule}')
        else:
            output_lines.append('\n所有预期规则均已存在')
        
        # 输出到控制台
        for line in output_lines:
            print(line)
        
        # 保存到文件
        report_file = os.path.join(project_root, 'scripts', 'rules_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            for line in output_lines:
                f.write(line + '\n')
        
        print(f'\n报告已保存到 {report_file} 文件中')

if __name__ == "__main__":
    check_game_rules()