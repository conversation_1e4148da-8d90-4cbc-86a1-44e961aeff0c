#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新成就奖励系统，添加金钱奖励字段
"""

from app import create_app, db
from app.models import Achievement
import sqlite3

def update_achievement_rewards():
    """更新成就奖励系统"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已经有reward_money字段
            try:
                # 尝试查询reward_money字段
                result = db.session.execute(db.text("SELECT reward_money FROM achievement LIMIT 1")).fetchone()
                print("✅ reward_money字段已存在")
            except Exception:
                # 如果字段不存在，添加字段
                print("📝 添加reward_money字段...")
                db.session.execute(db.text("ALTER TABLE achievement ADD COLUMN reward_money INTEGER DEFAULT 0"))
                db.session.commit()
                print("✅ reward_money字段添加成功")
            
            # 更新现有成就的奖励
            achievements = Achievement.query.all()
            print(f"📊 找到 {len(achievements)} 个成就，开始更新奖励...")

            updated_count = 0
            for achievement in achievements:
                old_money = getattr(achievement, 'reward_money', 0)
                old_reputation = achievement.reward_reputation

                # 根据成就类型设置不同的奖励
                if achievement.category == '财务成就':
                    achievement.reward_money = 50000
                    achievement.reward_reputation = 200
                elif achievement.category == '员工成就':
                    achievement.reward_money = 20000
                    achievement.reward_reputation = 150
                elif achievement.category == '发展成就':
                    achievement.reward_money = 100000
                    achievement.reward_reputation = 300
                elif achievement.category == '经营成就':
                    achievement.reward_money = 30000
                    achievement.reward_reputation = 250
                elif achievement.category == '特殊成就':
                    achievement.reward_money = 200000
                    achievement.reward_reputation = 500
                else:
                    # 默认奖励
                    achievement.reward_money = 10000
                    achievement.reward_reputation = 100

                updated_count += 1
                print(f"  📝 更新成就: {achievement.name}")
                print(f"    旧奖励: ¥{old_money:,}, {old_reputation}声望")
                print(f"    新奖励: ¥{achievement.reward_money:,}, {achievement.reward_reputation}声望")

            # 强制提交更改
            try:
                db.session.commit()
                print(f"✅ 成功更新 {updated_count} 个成就的奖励")
            except Exception as commit_error:
                print(f"❌ 提交更改失败: {commit_error}")
                db.session.rollback()
                return False
            
            # 验证更新结果
            print("\n📋 验证更新结果:")
            for category in ['财务成就', '员工成就', '发展成就', '经营成就', '特殊成就']:
                count = Achievement.query.filter_by(category=category).count()
                if count > 0:
                    sample = Achievement.query.filter_by(category=category).first()
                    print(f"  {category}: {count}个成就, 示例奖励 - 金钱:{sample.reward_money}, 声望:{sample.reward_reputation}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 更新成就奖励失败: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("🔧 开始更新成就奖励系统...")
    if update_achievement_rewards():
        print("🎉 成就奖励系统更新完成！")
    else:
        print("❌ 成就奖励系统更新失败！")
