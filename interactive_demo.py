#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交互式功能演示脚本
展示所有可操作的功能点
"""

import requests
import json
import time
import webbrowser
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def api_call(endpoint, method="GET", data=None):
    """API调用封装"""
    try:
        url = f"{BASE_URL}{endpoint}"
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            return True, response.json() if response.content else {}
        else:
            return False, f"HTTP {response.status_code}: {response.text}"
    except Exception as e:
        return False, str(e)

def interactive_employee_demo():
    """交互式员工管理演示"""
    print("\n🎯 员工管理系统交互演示")
    print("-" * 40)
    
    # 1. 显示当前候选人
    success, data = api_call("/employees/get_candidates_list")
    if success:
        candidates = data.get("candidates", [])
        print(f"📋 当前有 {len(candidates)} 名候选人:")
        for i, candidate in enumerate(candidates[:5]):
            print(f"  {i+1}. {candidate['name']} - {candidate['department']} - {candidate['level']}级 - ¥{candidate['salary']}/月")
    
    # 2. 招聘第一个候选人
    if candidates:
        candidate = candidates[0]
        print(f"\n🤝 招聘 {candidate['name']}...")
        success, data = api_call("/employees/hire", "POST", {"candidate_id": candidate["id"]})
        if success:
            print(f"✅ {data.get('message', '招聘成功')}")
        else:
            print(f"❌ 招聘失败: {data}")
    
    # 3. 显示当前员工
    success, data = api_call("/api/hotel_info")
    if success:
        print(f"\n👥 当前员工状况:")
        print(f"  总员工数: {data.get('total_employees', 0)}")
        print(f"  月工资支出: ¥{data.get('monthly_salary_expense', 0):,}")

def interactive_room_demo():
    """交互式房间管理演示"""
    print("\n🎯 房间管理系统交互演示")
    print("-" * 40)
    
    # 1. 显示当前房间状况
    success, data = api_call("/rooms/get_rooms_list")
    if success:
        rooms = data.get("rooms", [])
        print(f"🏠 当前房间状况:")
        for room in rooms:
            print(f"  {room['type']}: {room['count']}间 - ¥{room['price']}/晚 - 入住率{room['occupancy_rate']:.1f}%")
    
    # 2. 建设新房间
    print(f"\n🔨 建设2间单人间...")
    success, data = api_call("/rooms/build", "POST", {"room_type": "单人间", "quantity": 2})
    if success:
        print(f"✅ {data.get('message', '建设成功')}")
    else:
        print(f"❌ 建设失败: {data}")
    
    # 3. 调整房间价格
    print(f"\n💰 将单人间价格调整为¥380...")
    success, data = api_call("/rooms/set_price", "POST", {"room_type": "单人间", "price": 380})
    if success:
        print(f"✅ {data.get('message', '价格调整成功')}")
    else:
        print(f"❌ 价格调整失败: {data}")

def interactive_marketing_demo():
    """交互式营销管理演示"""
    print("\n🎯 营销管理系统交互演示")
    print("-" * 40)
    
    # 1. 显示营销活动
    success, data = api_call("/marketing/get_campaigns_list")
    if success:
        campaigns = data.get("campaigns", [])
        print(f"📢 营销活动状况:")
        active_count = sum(1 for c in campaigns if c.get('is_active'))
        print(f"  总活动数: {len(campaigns)}")
        print(f"  活跃活动: {active_count}")
        
        # 显示可用的营销活动
        available_campaigns = [c for c in campaigns if not c.get('is_active')]
        if available_campaigns:
            print(f"  可启动活动:")
            for campaign in available_campaigns[:3]:
                print(f"    - {campaign['name']}: ¥{campaign['cost']:,} - 持续{campaign['duration']}天")
    
    # 2. 启动一个营销活动
    if available_campaigns:
        # 使用字符串ID而不是数字ID
        campaign_id = "online_ads"  # 网络广告
        print(f"\n🚀 启动营销活动: 网络广告...")
        success, data = api_call("/marketing/start_campaign", "POST", {"campaign_id": campaign_id})
        if success:
            print(f"✅ {data.get('message', '营销活动启动成功')}")
        else:
            print(f"❌ 营销活动启动失败: {data}")

def interactive_time_demo():
    """交互式时间系统演示"""
    print("\n🎯 时间系统交互演示")
    print("-" * 40)
    
    # 1. 获取当前时间状态
    success, data = api_call("/api/hotel_info")
    if success:
        print(f"📅 当前游戏时间: {data.get('current_date')}")
        print(f"⏱️ 运营天数: {data.get('days_elapsed')}天")
    
    # 2. 暂停时间
    print(f"\n⏸️ 暂停时间...")
    success, data = api_call("/api/toggle_time", "POST")
    if success:
        print(f"✅ {data.get('message', '时间状态切换成功')}")
    
    # 3. 切换时间速度
    print(f"\n⚡ 切换时间速度...")
    success, data = api_call("/api/toggle_time_speed", "POST")
    if success:
        print(f"✅ {data.get('message', '时间速度切换成功')}")
    
    # 4. 手动推进一天
    print(f"\n⏭️ 手动推进一天...")
    success, data = api_call("/api/advance_time", "POST")
    if success:
        print(f"✅ {data.get('message', '时间推进成功')}")
    
    # 5. 恢复时间
    print(f"\n▶️ 恢复时间运行...")
    success, data = api_call("/api/toggle_time", "POST")
    if success:
        print(f"✅ {data.get('message', '时间状态切换成功')}")

def interactive_save_demo():
    """交互式存档系统演示"""
    print("\n🎯 存档系统交互演示")
    print("-" * 40)
    
    # 1. 查看存档槽
    success, data = api_call("/api/get_save_slots")
    if success:
        slots = data.get("slots", {})
        print(f"💾 存档槽状况:")
        for slot_name, slot_data in slots.items():
            if slot_data:
                print(f"  {slot_name}: {slot_data['hotel_name']} - {slot_data['date']} - {slot_data['level']}星")
            else:
                print(f"  {slot_name}: 空")
    
    # 2. 保存到槽位2
    print(f"\n💾 保存游戏到槽位2...")
    success, data = api_call("/api/save_to_slot", "POST", {"slot": 2})
    if success:
        print(f"✅ {data.get('message', '保存成功')}")
    
    # 3. 再次查看存档槽
    success, data = api_call("/api/get_save_slots")
    if success:
        slots = data.get("slots", {})
        print(f"\n💾 更新后的存档槽:")
        for slot_name, slot_data in slots.items():
            if slot_data:
                print(f"  {slot_name}: {slot_data['hotel_name']} - {slot_data['date']} - {slot_data['level']}星")
            else:
                print(f"  {slot_name}: 空")

def show_current_status():
    """显示当前酒店状态"""
    print("\n📊 当前酒店状态")
    print("=" * 60)
    
    success, data = api_call("/api/hotel_info")
    if success:
        print(f"🏨 酒店名称: {data['hotel_name']}")
        print(f"⭐ 酒店等级: {data['level']}星")
        print(f"💰 当前资金: ¥{data['money']:,}")
        print(f"📅 当前日期: {data['current_date']}")
        print(f"📈 运营天数: {data['days_elapsed']}天")
        print(f"😊 客户满意度: {data['satisfaction']}")
        print(f"🏆 声望值: {data['reputation']}")
        print(f"👥 员工总数: {data.get('total_employees', 0)}")
        print(f"🏠 房间总数: {data.get('total_rooms', 0)}")

def open_browser():
    """打开浏览器访问系统"""
    print(f"\n🌐 正在打开浏览器访问系统...")
    try:
        webbrowser.open(BASE_URL)
        print(f"✅ 浏览器已打开，访问地址: {BASE_URL}")
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print(f"💡 请手动访问: {BASE_URL}")

def main():
    """主演示函数"""
    print("🎮 酒店管理系统交互式功能演示")
    print("=" * 60)
    print("本演示将展示系统的所有可操作功能点")
    
    # 等待服务器启动
    print("\n⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 显示初始状态
    show_current_status()
    
    # 交互式演示各个功能
    interactive_time_demo()
    interactive_employee_demo()
    interactive_room_demo()
    interactive_marketing_demo()
    interactive_save_demo()
    
    # 显示最终状态
    print("\n" + "=" * 60)
    print("🎯 演示后的酒店状态")
    show_current_status()
    
    # 打开浏览器
    open_browser()
    
    print("\n" + "=" * 60)
    print("🎉 交互式演示完成！")
    print("💡 您现在可以通过浏览器体验完整的用户界面")
    print("🔧 所有功能都已实现并可正常使用")

if __name__ == "__main__":
    main()
