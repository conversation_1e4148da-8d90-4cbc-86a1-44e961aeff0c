#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
初始化成就系统
"""

from app import create_app, db
from app.models import Hotel, Achievement

def init_achievements():
    """初始化成就系统"""
    app = create_app()
    
    with app.app_context():
        hotel = Hotel.query.first()
        if not hotel:
            print("❌ 酒店数据未初始化")
            return
        
        # 检查是否已经有成就数据
        existing_achievements = Achievement.query.filter_by(hotel_id=hotel.id).count()
        if existing_achievements > 0:
            print(f"✅ 成就系统已初始化，共有 {existing_achievements} 个成就")
            return
        
        # 创建成就数据
        achievements_data = [
            # 财务成就
            {'name': '初次盈利', 'description': '酒店资金超过初始资金', 'category': '财务成就', 'reward': 100},
            {'name': '百万富翁', 'description': '酒店资金达到100万元', 'category': '财务成就', 'reward': 200},
            {'name': '千万富翁', 'description': '酒店资金达到1000万元', 'category': '财务成就', 'reward': 500},
            {'name': '亿万富翁', 'description': '酒店资金达到1亿元', 'category': '财务成就', 'reward': 2000},
            
            # 员工成就
            {'name': '首位员工', 'description': '招聘第一名员工', 'category': '员工成就', 'reward': 50},
            {'name': '十人团队', 'description': '拥有10名员工', 'category': '员工成就', 'reward': 100},
            {'name': '百人团队', 'description': '拥有100名员工', 'category': '员工成就', 'reward': 1000},
            
            # 发展成就
            {'name': '星光初现', 'description': '酒店达到2星', 'category': '发展成就', 'reward': 200},
            {'name': '三星荣耀', 'description': '酒店达到3星', 'category': '发展成就', 'reward': 300},
            {'name': '四海为家', 'description': '酒店达到4星', 'category': '发展成就', 'reward': 400},
            {'name': '五星级别', 'description': '酒店达到5星', 'category': '发展成就', 'reward': 500},
            {'name': '六六大顺', 'description': '酒店达到6星', 'category': '发展成就', 'reward': 600},
            {'name': '七星高照', 'description': '酒店达到7星', 'category': '发展成就', 'reward': 700},
            {'name': '八方来客', 'description': '酒店达到8星', 'category': '发展成就', 'reward': 800},
            {'name': '九霄云外', 'description': '酒店达到9星', 'category': '发展成就', 'reward': 1000},
            
            # 经营成就
            {'name': '满意服务', 'description': '客户满意度达到90分以上', 'category': '经营成就', 'reward': 300},
            {'name': '声望卓著', 'description': '声望值达到10000', 'category': '经营成就', 'reward': 500},
            {'name': '房间帝国', 'description': '拥有100间房间', 'category': '经营成就', 'reward': 800},
            
            # 特殊成就
            {'name': '长期经营', 'description': '运营超过10年(3650天)', 'category': '特殊成就', 'reward': 1000},
            {'name': '快速发展', 'description': '1年内达到5星', 'category': '特殊成就', 'reward': 2000},
        ]
        
        # 创建成就记录
        for achievement_data in achievements_data:
            achievement = Achievement(
                hotel_id=hotel.id,
                name=achievement_data['name'],
                description=achievement_data['description'],
                category=achievement_data['category'],
                condition_type='custom',
                condition_value=0,
                reward_reputation=achievement_data['reward'],
                achieved=False,
                reward_claimed=False
            )
            db.session.add(achievement)
        
        try:
            db.session.commit()
            print(f"✅ 成功初始化 {len(achievements_data)} 个成就")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 初始化成就失败: {e}")

if __name__ == "__main__":
    init_achievements()
