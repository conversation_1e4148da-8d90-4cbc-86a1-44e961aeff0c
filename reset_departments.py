import sys
import os
import sqlite3

# 直接通过SQL操作数据库，避免Flask应用上下文问题
def reset_departments():
    # 连接到数据库
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    # 更新部门状态
    cursor.execute("UPDATE department SET is_unlocked = 1 WHERE name IN ('前台部', '客房部')")
    cursor.execute("UPDATE department SET is_unlocked = 0 WHERE name NOT IN ('前台部', '客房部')")
    
    # 提交更改
    conn.commit()
    
    # 查询并显示更新后的状态
    cursor.execute("SELECT name, is_unlocked FROM department")
    departments = cursor.fetchall()
    
    print("部门状态已重置:")
    for name, is_unlocked in departments:
        status = "已解锁" if is_unlocked else "未解锁"
        print(f"{name}: {status}")
    
    # 关闭连接
    conn.close()

if __name__ == "__main__":
    reset_departments()