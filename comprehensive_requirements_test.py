#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
酒店管理系统全面需求测试脚本
对照REQUIREMENTS.md文档，全面测试所有功能的实现情况
"""

import requests
import json
import time
import sys
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:5000"
TEST_RESULTS = []

def log_test(test_name, status, details=""):
    """记录测试结果"""
    result = {
        "test_name": test_name,
        "status": status,  # PASS, FAIL, MISSING
        "details": details,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    TEST_RESULTS.append(result)
    status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_symbol} {test_name}: {details}")

def test_api_endpoint(endpoint, method="GET", data=None):
    """测试API端点"""
    try:
        url = f"{BASE_URL}{endpoint}"
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            return True, response.json() if response.content else {}
        else:
            return False, f"HTTP {response.status_code}: {response.text}"
    except Exception as e:
        return False, str(e)

def test_page_access(page_path, page_name):
    """测试页面访问"""
    try:
        url = f"{BASE_URL}{page_path}"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            log_test(f"页面访问 - {page_name}", "PASS", f"页面正常加载")
            return True
        else:
            log_test(f"页面访问 - {page_name}", "FAIL", f"HTTP {response.status_code}")
            return False
    except Exception as e:
        log_test(f"页面访问 - {page_name}", "FAIL", str(e))
        return False

def test_basic_hotel_info():
    """测试酒店基本信息管理功能"""
    print("\n=== 测试酒店基本信息管理 ===")

    # 测试获取酒店信息
    success, data = test_api_endpoint("/api/hotel_info")
    if success:
        # API直接返回酒店信息，不是嵌套在hotel字段中
        required_fields = ["hotel_name", "level", "money", "current_date", "days_elapsed", "satisfaction", "reputation"]
        missing_fields = [field for field in required_fields if field not in data]

        if not missing_fields:
            log_test("酒店基本信息字段", "PASS", f"所有必需字段都存在: {data['hotel_name']}, {data['level']}星")
        else:
            log_test("酒店基本信息字段", "FAIL", f"缺失字段: {missing_fields}")
    else:
        log_test("酒店基本信息API", "FAIL", data)

def test_time_system():
    """测试时间系统功能"""
    print("\n=== 测试时间系统 ===")

    # 测试时间控制API
    endpoints = [
        ("/api/toggle_time", "切换时间状态"),
        ("/api/toggle_time_speed", "切换时间速度")
    ]

    for endpoint, name in endpoints:
        success, data = test_api_endpoint(endpoint, "POST")
        if success:
            log_test(f"时间控制 - {name}", "PASS", "API响应正常")
        else:
            log_test(f"时间控制 - {name}", "FAIL", data)

def test_employee_management():
    """测试员工管理系统"""
    print("\n=== 测试员工管理系统 ===")

    # 测试获取候选人列表
    success, data = test_api_endpoint("/employees/get_candidates_list")
    if success:
        candidates = data.get("candidates", [])
        log_test("员工候选人列表", "PASS", f"获取到 {len(candidates)} 名候选人")

        # 测试招聘功能 - 使用第一个候选人
        if candidates:
            recruit_data = {
                "candidate_id": candidates[0]["id"]
            }
            success, data = test_api_endpoint("/employees/hire", "POST", recruit_data)
            if success:
                log_test("员工招聘功能", "PASS", "招聘API响应正常")
            else:
                log_test("员工招聘功能", "FAIL", data)
        else:
            log_test("员工招聘功能", "FAIL", "没有可用的候选人")
    else:
        log_test("员工候选人列表", "FAIL", data)

def test_department_management():
    """测试部门管理系统"""
    print("\n=== 测试部门管理系统 ===")
    
    # 测试获取部门列表
    success, data = test_api_endpoint("/departments/get_departments_list")
    if success:
        departments = data.get("departments", [])
        log_test("部门列表获取", "PASS", f"获取到 {len(departments)} 个部门")
        
        # 检查部门解锁功能
        unlocked_count = sum(1 for dept in departments if dept.get("is_unlocked"))
        log_test("部门解锁状态", "PASS", f"{unlocked_count} 个部门已解锁")
    else:
        log_test("部门列表获取", "FAIL", data)

def test_room_management():
    """测试房间管理系统"""
    print("\n=== 测试房间管理系统 ===")
    
    # 测试获取房间列表
    success, data = test_api_endpoint("/rooms/get_rooms_list")
    if success:
        rooms = data.get("rooms", [])
        log_test("房间列表获取", "PASS", f"获取到 {len(rooms)} 种房间类型")
        
        # 测试房间建设功能
        build_data = {
            "room_type": "单人间",
            "count": 1
        }
        success, data = test_api_endpoint("/rooms/build", "POST", build_data)
        if success:
            log_test("房间建设功能", "PASS", "建设API响应正常")
        else:
            log_test("房间建设功能", "FAIL", data)
    else:
        log_test("房间列表获取", "FAIL", data)

def test_marketing_system():
    """测试营销管理系统"""
    print("\n=== 测试营销管理系统 ===")

    # 测试获取营销活动列表
    success, data = test_api_endpoint("/marketing/get_campaigns_list")
    if success:
        log_test("营销活动列表", "PASS", "获取营销活动成功")
    else:
        log_test("营销活动列表", "FAIL", data)

def test_finance_system():
    """测试财务管理系统"""
    print("\n=== 测试财务管理系统 ===")

    # 测试财务记录获取
    success, data = test_api_endpoint("/finance/get_financial_data")
    if success:
        records = data.get("records", [])
        log_test("财务记录获取", "PASS", f"获取到 {len(records)} 条财务记录")
    else:
        log_test("财务记录获取", "FAIL", data)

def test_achievement_system():
    """测试成就系统"""
    print("\n=== 测试成就系统 ===")

    # 测试成就列表获取
    success, data = test_api_endpoint("/achievements/get_achievements_list")
    if success:
        achievements = data.get("achievements", [])
        log_test("成就系统", "PASS", f"获取到 {len(achievements)} 个成就")
    else:
        log_test("成就系统", "FAIL", data)

def test_hotel_upgrade():
    """测试酒店升级系统"""
    print("\n=== 测试酒店升级系统 ===")

    # 测试酒店升级页面（包含升级信息）
    success = test_page_access("/hotel/management", "酒店升级页面")
    if success:
        log_test("酒店升级信息", "PASS", "升级页面正常访问")
    else:
        log_test("酒店升级信息", "FAIL", "升级页面访问失败")

def test_all_pages():
    """测试所有页面访问"""
    print("\n=== 测试页面访问 ===")
    
    pages = [
        ("/", "首页"),
        ("/employees/management", "员工管理"),
        ("/departments/management", "部门管理"),
        ("/rooms/management", "房间管理"),
        ("/hotel/management", "酒店升级"),
        ("/marketing/management", "营销管理"),
        ("/finance/management", "财务管理"),
        ("/achievements/management", "成就系统")
    ]
    
    for page_path, page_name in pages:
        test_page_access(page_path, page_name)

def test_calculation_rules():
    """测试计算规则"""
    print("\n=== 测试计算规则 ===")

    # 获取当前酒店信息来验证计算
    success, data = test_api_endpoint("/api/hotel_info")
    if success:
        # 检查满意度是否在合理范围
        satisfaction = data.get("satisfaction", 0)
        if 0 <= satisfaction <= 100:
            log_test("满意度计算", "PASS", f"满意度: {satisfaction}")
        else:
            log_test("满意度计算", "FAIL", f"满意度超出范围: {satisfaction}")

        # 检查声望值
        reputation = data.get("reputation", 0)
        if reputation >= 0:
            log_test("声望值计算", "PASS", f"声望值: {reputation}")
        else:
            log_test("声望值计算", "FAIL", f"声望值为负: {reputation}")
    else:
        log_test("计算规则验证", "FAIL", "无法获取酒店信息")

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    total_tests = len(TEST_RESULTS)
    passed_tests = sum(1 for result in TEST_RESULTS if result["status"] == "PASS")
    failed_tests = sum(1 for result in TEST_RESULTS if result["status"] == "FAIL")
    missing_tests = sum(1 for result in TEST_RESULTS if result["status"] == "MISSING")
    
    print(f"总测试数: {total_tests}")
    print(f"✅ 通过: {passed_tests}")
    print(f"❌ 失败: {failed_tests}")
    print(f"⚠️ 缺失: {missing_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if failed_tests > 0 or missing_tests > 0:
        print("\n🔍 需要关注的问题:")
        for result in TEST_RESULTS:
            if result["status"] in ["FAIL", "MISSING"]:
                print(f"  {result['status']}: {result['test_name']} - {result['details']}")
    
    # 保存详细报告
    with open("test_report.json", "w", encoding="utf-8") as f:
        json.dump(TEST_RESULTS, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试报告已保存到: test_report.json")

def test_detailed_requirements():
    """详细测试需求文档中的具体功能"""
    print("\n=== 详细需求功能测试 ===")

    # 测试员工解雇功能
    success, data = test_api_endpoint("/employees/get_candidates_list")
    if success:
        candidates = data.get("candidates", [])
        if candidates:
            # 先招聘一个员工
            hire_data = {"candidate_id": candidates[0]["id"]}
            success, data = test_api_endpoint("/employees/hire", "POST", hire_data)
            if success:
                # 测试解雇功能
                fire_data = {"employee_id": 1}  # 假设员工ID为1
                success, data = test_api_endpoint("/employees/fire", "POST", fire_data)
                if success:
                    log_test("员工解雇功能", "PASS", "解雇功能正常")
                else:
                    log_test("员工解雇功能", "FAIL", data)

    # 测试部门解锁功能
    unlock_data = {"department_name": "客房部"}
    success, data = test_api_endpoint("/departments/unlock", "POST", unlock_data)
    if success:
        log_test("部门解锁功能", "PASS", "部门解锁功能正常")
    else:
        log_test("部门解锁功能", "FAIL", data)

    # 测试房间价格设置
    price_data = {"room_type": "单人间", "price": 350}
    success, data = test_api_endpoint("/rooms/set_price", "POST", price_data)
    if success:
        log_test("房间价格设置", "PASS", "价格设置功能正常")
    else:
        log_test("房间价格设置", "FAIL", data)

    # 测试营销活动启动
    campaign_data = {"campaign_type": "广告投放", "budget": 10000}
    success, data = test_api_endpoint("/marketing/start_campaign", "POST", campaign_data)
    if success:
        log_test("营销活动启动", "PASS", "营销活动启动正常")
    else:
        log_test("营销活动启动", "FAIL", data)

    # 测试存档功能
    save_data = {"slot": 1}
    success, data = test_api_endpoint("/api/save_to_slot", "POST", save_data)
    if success:
        log_test("游戏存档功能", "PASS", "存档功能正常")
    else:
        log_test("游戏存档功能", "FAIL", data)

def main():
    """主测试函数"""
    print("🚀 开始酒店管理系统全面功能测试")
    print(f"测试目标: {BASE_URL}")
    print("="*60)

    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)

    # 执行所有测试
    test_all_pages()
    test_basic_hotel_info()
    test_time_system()
    test_employee_management()
    test_department_management()
    test_room_management()
    test_marketing_system()
    test_finance_system()
    test_achievement_system()
    test_hotel_upgrade()
    test_calculation_rules()
    test_detailed_requirements()

    # 生成测试报告
    generate_test_report()

if __name__ == "__main__":
    main()
