#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_recruitment_with_retry():
    """测试带重试机制的招聘功能"""
    print('🧪 测试招聘功能（带重试机制）...')
    
    # 先获取候选人
    try:
        r = requests.get('http://127.0.0.1:5000/employees/get_candidates_list')
        if r.status_code == 200:
            data = r.json()
            if data.get('success'):
                candidates_count = len(data.get('candidates', []))
                print(f'✅ 候选人获取成功: {candidates_count}个')
                
                # 测试招聘
                hire_data = {'candidate_id': 'candidate_0'}
                hire_r = requests.post('http://127.0.0.1:5000/employees/hire', 
                                     headers={'Content-Type': 'application/json'},
                                     data=json.dumps(hire_data))
                
                print(f'招聘状态码: {hire_r.status_code}')
                if hire_r.status_code == 200:
                    result = hire_r.json()
                    print(f'✅ 招聘成功: {result.get("success")}')
                    print(f'招聘信息: {result.get("message")}')
                else:
                    print(f'❌ 招聘失败: {hire_r.text[:200]}')
            else:
                print(f'❌ 候选人获取失败: {data.get("message")}')
        else:
            print(f'❌ 候选人API错误: {r.status_code}')
    except Exception as e:
        print(f'❌ 请求异常: {e}')

def test_multiple_operations():
    """测试多个操作，验证重试机制"""
    print('\n🔄 测试多个操作（验证重试机制）...')
    
    operations = [
        ('获取候选人', 'GET', '/employees/get_candidates_list', None),
        ('获取酒店信息', 'GET', '/api/hotel_info', None),
        ('获取员工列表', 'GET', '/employees/management', None),
    ]
    
    for name, method, url, data in operations:
        try:
            if method == 'GET':
                r = requests.get(f'http://127.0.0.1:5000{url}')
            else:
                r = requests.post(f'http://127.0.0.1:5000{url}', 
                                headers={'Content-Type': 'application/json'},
                                data=json.dumps(data) if data else None)
            
            if r.status_code == 200:
                print(f'✅ {name}: 成功')
            else:
                print(f'❌ {name}: HTTP {r.status_code}')
                
        except Exception as e:
            print(f'❌ {name}: 异常 - {e}')
        
        time.sleep(0.5)  # 短暂延迟避免过快请求

def test_database_operations():
    """测试数据库操作密集场景"""
    print('\n💾 测试数据库操作密集场景...')
    
    # 连续进行多个可能引起数据库锁定的操作
    for i in range(5):
        print(f'第{i+1}次操作...')
        
        # 获取候选人
        try:
            r = requests.get('http://127.0.0.1:5000/employees/get_candidates_list')
            if r.status_code == 200:
                print(f'  ✅ 候选人获取: 成功')
            else:
                print(f'  ❌ 候选人获取: HTTP {r.status_code}')
        except Exception as e:
            print(f'  ❌ 候选人获取: 异常 - {e}')
        
        time.sleep(1)  # 等待1秒

if __name__ == '__main__':
    print('🚀 开始数据库锁定修复测试...')
    
    test_recruitment_with_retry()
    test_multiple_operations()
    test_database_operations()
    
    print('\n✅ 测试完成！')
