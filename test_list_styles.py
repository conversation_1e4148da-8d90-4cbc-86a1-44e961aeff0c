#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_list_style_pages():
    """测试所有改为列表样式的页面"""
    pages = [
        ('部门管理', '/departments/management'),
        ('房间管理', '/rooms/management'),
        ('营销管理', '/marketing/management'),
        ('酒店升级', '/hotel/management'),
        ('首页（入住率图表）', '/'),
    ]

    print('🎨 测试列表样式页面...')
    success_count = 0
    for name, url in pages:
        try:
            r = requests.get(f'http://127.0.0.1:5000{url}')
            if r.status_code == 200:
                print(f'✅ {name}: 正常访问')
                success_count += 1
            else:
                print(f'❌ {name}: HTTP {r.status_code}')
        except Exception as e:
            print(f'❌ {name}: 异常 - {e}')
    
    print(f'列表样式页面测试结果: {success_count}/{len(pages)} 成功')
    return success_count == len(pages)

def test_occupancy_chart_data():
    """测试入住率图表数据（一整月）"""
    print('\n📊 测试入住率图表数据...')
    try:
        r = requests.get('http://127.0.0.1:5000/api/occupancy_data')
        if r.status_code == 200:
            data = r.json()
            print('✅ 入住率数据API: 正常')
            
            # 检查数据结构
            if isinstance(data, dict):
                for room_type, occupancy_list in data.items():
                    if isinstance(occupancy_list, list) and len(occupancy_list) > 10:
                        print(f'✅ {room_type}: {len(occupancy_list)}天数据（月度数据）')
                    else:
                        print(f'⚠️ {room_type}: {len(occupancy_list)}天数据（可能不是月度）')
                return True
            else:
                print('❌ 数据格式错误')
                return False
        else:
            print(f'❌ 入住率数据API: HTTP {r.status_code}')
            return False
    except Exception as e:
        print(f'❌ 入住率数据API: 异常 - {e}')
        return False

def test_hotel_info():
    """测试酒店基本信息"""
    print('\n🏨 测试酒店信息...')
    try:
        r = requests.get('http://127.0.0.1:5000/api/hotel_info')
        if r.status_code == 200:
            data = r.json()
            print('✅ 酒店信息API: 正常')
            print(f'   酒店名称: {data.get("name")}')
            print(f'   酒店等级: {data.get("level")}星')
            print(f'   当前日期: {data.get("date")}')
            print(f'   资金: ¥{data.get("money"):,}')
            print(f'   满意度: {data.get("satisfaction"):.1f}分')
            print(f'   声望: {data.get("reputation")} ({data.get("reputation_level")})')
            return True
        else:
            print(f'❌ 酒店信息API: HTTP {r.status_code}')
            return False
    except Exception as e:
        print(f'❌ 酒店信息API: 异常 - {e}')
        return False

def test_specific_features():
    """测试特定功能"""
    print('\n🔧 测试特定功能...')
    
    # 测试部门解锁
    try:
        r = requests.get('http://127.0.0.1:5000/departments/management')
        if r.status_code == 200:
            print('✅ 部门管理页面: 列表样式正常')
        else:
            print(f'❌ 部门管理页面: HTTP {r.status_code}')
    except Exception as e:
        print(f'❌ 部门管理页面: {e}')
    
    # 测试房间建设
    try:
        r = requests.get('http://127.0.0.1:5000/rooms/management')
        if r.status_code == 200:
            print('✅ 房间管理页面: 列表样式正常')
        else:
            print(f'❌ 房间管理页面: HTTP {r.status_code}')
    except Exception as e:
        print(f'❌ 房间管理页面: {e}')
    
    # 测试营销活动
    try:
        r = requests.get('http://127.0.0.1:5000/marketing/management')
        if r.status_code == 200:
            print('✅ 营销管理页面: 列表样式正常')
        else:
            print(f'❌ 营销管理页面: HTTP {r.status_code}')
    except Exception as e:
        print(f'❌ 营销管理页面: {e}')
    
    # 测试酒店升级
    try:
        r = requests.get('http://127.0.0.1:5000/hotel/management')
        if r.status_code == 200:
            print('✅ 酒店升级页面: 列表样式正常')
        else:
            print(f'❌ 酒店升级页面: HTTP {r.status_code}')
    except Exception as e:
        print(f'❌ 酒店升级页面: {e}')

if __name__ == '__main__':
    print('🚀 开始测试列表样式和入住率图表改进...')
    
    pages_ok = test_list_style_pages()
    chart_ok = test_occupancy_chart_data()
    info_ok = test_hotel_info()
    
    test_specific_features()
    
    print('\n📊 测试结果总结:')
    print(f'✅ 列表样式页面: {"通过" if pages_ok else "失败"}')
    print(f'✅ 入住率图表: {"通过" if chart_ok else "失败"}')
    print(f'✅ 酒店信息: {"通过" if info_ok else "失败"}')
    
    if pages_ok and chart_ok and info_ok:
        print('\n🎉 所有列表样式和图表改进测试通过！')
        print('💡 改进效果:')
        print('   - 部门、房间、营销、升级页面统一为列表样式')
        print('   - 入住率图表显示当前一整月数据')
        print('   - 数据展示更加清晰和专业')
    else:
        print('\n⚠️ 部分功能还有问题，需要进一步调试。')
