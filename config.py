#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
项目配置文件
包含数据库连接配置和日志配置等
"""

import os

class Config:
    """基础配置类"""
    # 使用本地 SQLite 数据库
    basedir = os.path.abspath(os.path.dirname(__file__))
    SQLALCHEMY_DATABASE_URI = 'sqlite:///' + os.path.join(basedir, 'instance', 'hotel.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_pre_ping": True,
    }
    
    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'logs/project.log'
    LOG_MAX_BYTES = int(os.environ.get('LOG_MAX_BYTES') or 10240)
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT') or 10)