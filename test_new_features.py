#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_all_features():
    print('🧪 测试所有新功能...')
    
    # 测试所有页面访问
    pages = [
        ('首页', 'http://127.0.0.1:5000/'),
        ('部门管理', 'http://127.0.0.1:5000/departments/management'),
        ('员工管理', 'http://127.0.0.1:5000/employees/management'),
        ('房间管理', 'http://127.0.0.1:5000/rooms/management'),
        ('财务管理', 'http://127.0.0.1:5000/finance/management'),
        ('酒店升级', 'http://127.0.0.1:5000/hotel/management'),
        ('营销管理', 'http://127.0.0.1:5000/marketing/management')
    ]
    
    print('\n📄 页面访问测试:')
    for name, url in pages:
        try:
            r = requests.get(url, timeout=10)
            if r.status_code == 200:
                print(f'✅ {name}: 正常访问')
            else:
                print(f'❌ {name}: HTTP {r.status_code}')
        except Exception as e:
            print(f'❌ {name}: 请求失败')
    
    # 测试新功能API
    print('\n🔧 新功能API测试:')
    
    # 测试招聘候选人列表
    try:
        r = requests.get('http://127.0.0.1:5000/employees/get_candidates_list')
        if r.status_code == 200:
            data = r.json()
            if data.get('success'):
                candidates_count = len(data.get('candidates', []))
                print(f'✅ 招聘候选人: 获取到{candidates_count}个候选人')
            else:
                print(f'❌ 招聘候选人: {data.get("message", "未知错误")}')
        else:
            print(f'❌ 招聘候选人: HTTP {r.status_code}')
    except Exception as e:
        print(f'❌ 招聘候选人: 请求失败 - {e}')
    
    # 测试酒店信息API
    try:
        r = requests.get('http://127.0.0.1:5000/api/hotel_info')
        if r.status_code == 200:
            data = r.json()
            if data.get('success'):
                print(f'✅ 酒店信息: {data.get("hotel_name")}, {data.get("level")}星, ¥{data.get("money"):,}')
            else:
                print(f'❌ 酒店信息: {data.get("message", "未知错误")}')
        else:
            print(f'❌ 酒店信息: HTTP {r.status_code}')
    except Exception as e:
        print(f'❌ 酒店信息: 请求失败 - {e}')
    
    print('\n🎉 功能测试完成！')

if __name__ == '__main__':
    test_all_features()
