#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
酒店管理系统全面测试脚本
测试所有页面、功能和按钮操作
"""

import requests
import json
import time

BASE_URL = 'http://127.0.0.1:5000'

class HotelSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = {
            'pages': {},
            'apis': {},
            'functions': {},
            'buttons': {}
        }
    
    def test_page_access(self, name, url):
        """测试页面访问"""
        try:
            r = self.session.get(f'{BASE_URL}{url}')
            success = r.status_code == 200
            self.test_results['pages'][name] = {
                'status': 'PASS' if success else 'FAIL',
                'status_code': r.status_code,
                'error': None if success else r.text[:200]
            }
            return success
        except Exception as e:
            self.test_results['pages'][name] = {
                'status': 'ERROR',
                'status_code': None,
                'error': str(e)
            }
            return False
    
    def test_api_endpoint(self, name, url, method='GET', data=None):
        """测试API端点"""
        try:
            if method == 'GET':
                r = self.session.get(f'{BASE_URL}{url}')
            else:
                r = self.session.post(f'{BASE_URL}{url}', 
                                    headers={'Content-Type': 'application/json'},
                                    data=json.dumps(data) if data else None)
            
            success = r.status_code == 200
            response_data = r.json() if success else None
            
            self.test_results['apis'][name] = {
                'status': 'PASS' if success else 'FAIL',
                'status_code': r.status_code,
                'data': response_data,
                'error': None if success else r.text[:200]
            }
            return success, response_data
        except Exception as e:
            self.test_results['apis'][name] = {
                'status': 'ERROR',
                'status_code': None,
                'data': None,
                'error': str(e)
            }
            return False, None
    
    def test_all_pages(self):
        """测试所有页面访问"""
        print('🌐 测试页面访问...')
        
        pages = [
            ('首页', '/'),
            ('部门管理', '/departments/management'),
            ('员工管理', '/employees/management'),
            ('房间管理', '/rooms/management'),
            ('财务管理', '/finance/management'),
            ('酒店升级', '/hotel/management'),
            ('营销管理', '/marketing/management'),
            ('成就系统', '/achievements/management')
        ]
        
        for name, url in pages:
            success = self.test_page_access(name, url)
            status = '✅' if success else '❌'
            print(f'  {status} {name}: {self.test_results["pages"][name]["status"]}')
    
    def test_all_apis(self):
        """测试所有API端点"""
        print('\n🔌 测试API端点...')
        
        # 基础信息API
        success, data = self.test_api_endpoint('酒店信息', '/api/hotel_info')
        print(f'  {"✅" if success else "❌"} 酒店信息API')
        
        success, data = self.test_api_endpoint('入住率数据', '/api/occupancy_data')
        print(f'  {"✅" if success else "❌"} 入住率数据API')
        
        # 员工相关API
        success, data = self.test_api_endpoint('候选人列表', '/employees/get_candidates_list')
        print(f'  {"✅" if success else "❌"} 候选人列表API')
        
        # 如果候选人API成功，测试招聘功能
        if success and data and data.get('success'):
            hire_data = {'candidate_id': 'candidate_0'}
            success, result = self.test_api_endpoint('招聘员工', '/employees/hire', 'POST', hire_data)
            print(f'  {"✅" if success else "❌"} 招聘员工API')
    
    def test_functional_features(self):
        """测试功能特性"""
        print('\n⚙️ 测试功能特性...')
        
        # 测试时间推进系统
        success, data = self.test_api_endpoint('酒店信息1', '/api/hotel_info')
        if success:
            initial_date = data.get('date')
            time.sleep(6)  # 等待时间推进
            success2, data2 = self.test_api_endpoint('酒店信息2', '/api/hotel_info')
            if success2:
                new_date = data2.get('date')
                time_advanced = initial_date != new_date
                print(f'  {"✅" if time_advanced else "❌"} 时间推进系统: {"正常" if time_advanced else "异常"}')
            else:
                print('  ❌ 时间推进系统: 无法验证')
        else:
            print('  ❌ 时间推进系统: 无法测试')
        
        # 测试数据一致性
        success, data = self.test_api_endpoint('数据一致性', '/api/hotel_info')
        if success:
            required_fields = ['level', 'money', 'satisfaction', 'reputation', 'days_elapsed']
            missing_fields = [field for field in required_fields if field not in data]
            consistent = len(missing_fields) == 0
            print(f'  {"✅" if consistent else "❌"} 数据一致性: {"正常" if consistent else f"缺失{missing_fields}"}')
        else:
            print('  ❌ 数据一致性: 无法测试')
    
    def test_ui_elements(self):
        """测试UI元素"""
        print('\n🎨 测试UI元素...')
        
        # 测试首页内容
        try:
            r = self.session.get(f'{BASE_URL}/')
            if r.status_code == 200:
                content = r.text
                
                # 检查关键UI元素
                ui_elements = {
                    '游戏时间显示': 'currentDate' in content,
                    '酒店等级显示': 'hotelLevel' in content,
                    '资金显示': 'hotelMoney' in content,
                    '满意度显示': 'satisfaction' in content,
                    '声望显示': 'reputation' in content,
                    '入住率图表': 'occupancyChart' in content,
                    '时间控制按钮': 'toggleTimeBtn' in content,
                    '快捷管理按钮': 'departments.management' in content
                }
                
                for element, present in ui_elements.items():
                    print(f'  {"✅" if present else "❌"} {element}: {"存在" if present else "缺失"}')
            else:
                print('  ❌ 无法访问首页进行UI测试')
        except Exception as e:
            print(f'  ❌ UI测试异常: {e}')
    
    def test_list_style_pages(self):
        """测试列表样式页面"""
        print('\n📋 测试列表样式页面...')
        
        list_pages = [
            ('部门管理', '/departments/management'),
            ('房间管理', '/rooms/management'),
            ('营销管理', '/marketing/management'),
            ('酒店升级', '/hotel/management')
        ]
        
        for name, url in list_pages:
            try:
                r = self.session.get(f'{BASE_URL}{url}')
                if r.status_code == 200:
                    content = r.text
                    # 检查是否包含表格样式
                    has_table = 'table-responsive' in content or 'table table-hover' in content
                    print(f'  {"✅" if has_table else "❌"} {name}: {"列表样式" if has_table else "非列表样式"}')
                else:
                    print(f'  ❌ {name}: HTTP {r.status_code}')
            except Exception as e:
                print(f'  ❌ {name}: 异常 - {e}')
    
    def generate_report(self):
        """生成测试报告"""
        print('\n📊 测试报告总结:')
        print('=' * 50)
        
        # 页面测试统计
        page_pass = sum(1 for result in self.test_results['pages'].values() if result['status'] == 'PASS')
        page_total = len(self.test_results['pages'])
        print(f'📄 页面访问: {page_pass}/{page_total} 通过')
        
        # API测试统计
        api_pass = sum(1 for result in self.test_results['apis'].values() if result['status'] == 'PASS')
        api_total = len(self.test_results['apis'])
        print(f'🔌 API端点: {api_pass}/{api_total} 通过')
        
        # 总体评估
        total_pass = page_pass + api_pass
        total_tests = page_total + api_total
        success_rate = (total_pass / total_tests * 100) if total_tests > 0 else 0
        
        print(f'🎯 总体成功率: {success_rate:.1f}% ({total_pass}/{total_tests})')
        
        if success_rate >= 90:
            print('🎉 系统状态: 优秀！')
        elif success_rate >= 75:
            print('✅ 系统状态: 良好')
        elif success_rate >= 60:
            print('⚠️ 系统状态: 一般，需要改进')
        else:
            print('❌ 系统状态: 需要修复')
        
        # 详细错误报告
        print('\n🔍 详细错误信息:')
        for category, tests in self.test_results.items():
            for name, result in tests.items():
                if result['status'] != 'PASS':
                    print(f'  ❌ {name}: {result["status"]} - {result.get("error", "未知错误")[:100]}')

def main():
    print('🚀 开始酒店管理系统全面测试...')
    print('=' * 50)
    
    tester = HotelSystemTester()
    
    # 执行所有测试
    tester.test_all_pages()
    tester.test_all_apis()
    tester.test_functional_features()
    tester.test_ui_elements()
    tester.test_list_style_pages()
    
    # 生成报告
    tester.generate_report()
    
    print('\n✅ 测试完成！')

if __name__ == '__main__':
    main()
