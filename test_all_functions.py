#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_all_pages():
    """测试所有页面"""
    pages = [
        ('首页', '/'),
        ('员工管理', '/employees/management'),
        ('房间管理', '/rooms/management'),
        ('财务管理', '/finance/management'),
        ('酒店升级', '/hotel/management'),
        ('营销管理', '/marketing/management'),
        ('成就系统', '/achievements/management')
    ]

    print('🧪 测试所有页面...')
    success_count = 0
    for name, url in pages:
        try:
            r = requests.get(f'http://127.0.0.1:5000{url}')
            if r.status_code == 200:
                print(f'✅ {name}: 正常')
                success_count += 1
            else:
                print(f'❌ {name}: HTTP {r.status_code}')
        except Exception as e:
            print(f'❌ {name}: 异常 - {e}')
    
    print(f'页面测试结果: {success_count}/{len(pages)} 成功')
    return success_count == len(pages)

def test_recruitment():
    """测试招聘功能"""
    print('\n🧪 测试招聘功能...')
    try:
        # 获取候选人
        r = requests.get('http://127.0.0.1:5000/employees/get_candidates_list')
        if r.status_code == 200:
            print('✅ 获取候选人: 正常')
            
            # 测试招聘
            hire_data = {'candidate_id': 'candidate_0'}
            r = requests.post('http://127.0.0.1:5000/employees/hire', 
                             headers={'Content-Type': 'application/json'},
                             data=json.dumps(hire_data))
            if r.status_code == 200:
                result = r.json()
                if result.get('success'):
                    print('✅ 招聘员工: 正常')
                    print(f'   招聘信息: {result.get("message")}')
                    return True
                else:
                    print(f'❌ 招聘员工: {result.get("message")}')
                    return False
            else:
                print(f'❌ 招聘员工: HTTP {r.status_code}')
                return False
        else:
            print(f'❌ 获取候选人: HTTP {r.status_code}')
            return False
    except Exception as e:
        print(f'❌ 招聘功能: 异常 - {e}')
        return False

def test_hotel_info():
    """测试酒店信息API"""
    print('\n🧪 测试酒店信息API...')
    try:
        r = requests.get('http://127.0.0.1:5000/api/hotel_info')
        if r.status_code == 200:
            data = r.json()
            print('✅ 酒店信息API: 正常')
            print(f'   酒店名称: {data.get("name")}')
            print(f'   酒店等级: {data.get("level")}星')
            print(f'   资金: ¥{data.get("money"):,}')
            print(f'   满意度: {data.get("satisfaction"):.1f}分')
            return True
        else:
            print(f'❌ 酒店信息API: HTTP {r.status_code}')
            return False
    except Exception as e:
        print(f'❌ 酒店信息API: 异常 - {e}')
        return False

if __name__ == '__main__':
    print('🚀 开始全面功能测试...')
    
    pages_ok = test_all_pages()
    recruitment_ok = test_recruitment()
    api_ok = test_hotel_info()
    
    print('\n📊 测试结果总结:')
    print(f'✅ 页面访问: {"通过" if pages_ok else "失败"}')
    print(f'✅ 招聘功能: {"通过" if recruitment_ok else "失败"}')
    print(f'✅ API接口: {"通过" if api_ok else "失败"}')
    
    if pages_ok and recruitment_ok and api_ok:
        print('\n🎉 所有功能测试通过！数据库锁定问题彻底解决！')
    else:
        print('\n⚠️ 部分功能还有问题，需要进一步调试。')
