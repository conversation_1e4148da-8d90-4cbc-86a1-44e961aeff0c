#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app import create_app, db
from app.models import Employee, Room, Hotel
from app.main.utils import calculate_daily_finances
import traceback

app = create_app()
with app.app_context():
    print("=== 调试时间推进错误 ===")

    try:
        hotel = Hotel.query.first()
        if hotel:
            print(f"酒店: {hotel.name}")
            print(f"当前日期: {hotel.date}")
            print(f"资金: {hotel.money}")

            print("\n=== 尝试计算财务 ===")
            result = calculate_daily_finances(hotel)
            print(f"财务计算结果: {result}")
        else:
            print("没有找到酒店数据")

    except Exception as e:
        print(f"错误: {e}")
        print("详细错误信息:")
        traceback.print_exc()
