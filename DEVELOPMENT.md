# 酒店管理系统开发文档

## 1. 项目概述
酒店管理系统是一个基于Web的模拟经营游戏，玩家扮演酒店经营者，通过管理员工、部门、房间等资源，提升酒店星级和服务质量，最终成为顶级酒店大亨。

## 2. 开发环境
- Python 3.8+
- Flask 2.0+
- SQLite 3+
- Bootstrap 5
- jQuery 3.6+

## 3. 目录结构
```
hotel-management-system/
├── app/                    # Flask应用目录
│   ├── __init__.py         # 应用工厂
│   ├── models.py           # 数据模型
│   ├── main/              # 主要功能模块
│   ├── employees/          # 员工管理模块
│   ├── departments/        # 部门管理模块
│   ├── rooms/              # 房间管理模块
│   ├── hotel/              # 酒店管理模块
│   ├── marketing/          # 营销管理模块
│   └── templates/         # HTML模板
├── static/                 # 静态文件
├── instance/               # 数据库文件
├── scripts/                # 脚本文件
├── tests/                  # 测试文件
├── REQUIREMENTS.md         # 需求文档
├── DESIGN.md              # 设计文档
├── DEVELOPMENT.md         # 开发文档
├── README.md              # 项目说明
└── run.py                 # 应用入口
```

## 4. 核心功能实现

### 4.1 酒店管理模块

#### 4.1.1 酒店信息管理
- 酒店具有名称、星级、当前日期、运行天数、资金、声望等基本信息
- 实现了酒店名称修改功能
- 实现了酒店升星功能，包括条件检查和费用扣除

#### 4.1.2 时间管理
- 实现了自动时间推进功能，每2秒推进一天
- 提供了暂停和恢复时间的功能
- 时间推进时会计算每日收入和支出
- 时间推进时会更新员工工龄和工资

### 4.2 员工管理模块

#### 4.2.1 员工信息管理
- 员工具有姓名、部门、等级、基础工资、当前工资、工龄等属性
- 实现了员工招聘功能
- 实现了员工解雇功能
- 实现了员工晋升功能

#### 4.2.2 员工工资管理
- 每月月初自动扣除所有员工工资
- 工资根据员工等级和工龄计算
- 实现了工资涨幅机制

### 4.3 部门管理模块

#### 4.3.1 部门信息管理
- 酒店具有多个部门：前台部、客房部、餐饮部、营销部、安保部、财务部、商务部、康养部、工程部、人事部、董事会
- 实现了部门解锁功能
- 实现了部门繁忙度计算功能

#### 4.3.2 部门收入计算
- 各部门根据其功能产生不同类型的收入
- 餐饮部收入 = 当天入住客户数 × 20 × 酒店星级 + 当天员工人数 × 5 × 酒店星级
- 营销部收入 = 酒店星级 × 1000元（日常运营收入）
- 康养部收入 = 客房数 × 入住率 × 2000元

### 4.4 房间管理模块

#### 4.4.1 房间信息管理
- 房间具有类型、数量、价格等属性
- 实现了房间添加功能
- 实现了房间价格计算功能

#### 4.4.2 房间收入计算
- 房间收入 = 房间数量 × 房间价格 × 入住率
- 不同星级房间具有不同的入住率范围
- 入住率每天随机生成并在当天保持一致

### 4.5 财务管理模块

#### 4.5.1 收入管理
- 实现了客房收入计算
- 实现了餐饮收入计算
- 实现了康养收入计算
- 实现了营销收入计算

#### 4.5.2 支出管理
- 实现了员工工资支出计算
- 实现了房间扩建支出计算
- 实现了营销活动支出计算

#### 4.5.3 财务记录
- 记录每日收入和支出明细
- 支持按日期查询财务记录

### 4.6 营销管理模块

#### 4.6.1 营销活动管理
- 实现了多种营销活动的举办功能
- 支持网络广告、电视广告、线下推广、商务活动等营销方式
- 营销活动可以提升酒店入住率

#### 4.6.2 营销效果计算
- 不同营销活动具有不同的费用和效果持续时间
- 营销活动可以叠加，但同类型活动不能重复进行

### 4.7 成就系统模块

#### 4.7.1 成就管理
- 实现了成就分类展示功能
- 实现了成就完成状态跟踪
- 实现了成就奖励领取功能

#### 4.7.2 成就奖励机制
- 完成成就可获得声望奖励
- 部分成就可获得特殊装饰或功能奖励
- 奖励需要手动领取

### 4.8 声望系统模块

#### 4.8.1 声望等级管理
- 实现了9个声望等级的划分
- 根据声望值自动更新声望等级
- 在界面中展示当前声望值和等级

#### 4.8.2 声望获取机制
- 完成成就可获得声望
- 客户满意度达到一定标准可获得声望
- 财务表现良好可获得声望

## 5. 数据库变更历史

### v1.0.0 (2025-07-28)
- 创建初始数据库结构
- 添加酒店、员工、部门、房间、财务记录等基本表

### v1.1.0 (2025-07-29)
- 添加成就系统相关表
- 添加游戏设置和规则设置表

### v1.2.0 (2025-07-30)
- 添加季节效果和随机事件表
- 添加房间解锁规则、员工等级规则和员工招聘规则表

### v1.3.0 (2025-08-01)
- 添加成就奖励领取状态字段

### v1.4.0 (2025-08-02)
- 调整部门解锁规则，1星酒店默认解锁人事部
- 更新部门解锁费用与需求文档一致
- 更新房间价格与需求文档一致

## 6. 接口变更历史

### 2025-08-02
- 更新酒店升级接口，修改部门和房间解锁规则
- 更新部门初始化数据，添加人事部和董事会
- 更新房间类型列表，添加皇宫套房等新类型

### 2025-07-30
- 添加成就奖励领取接口
- 添加成就分页查询接口
- 优化酒店信息查询接口

### 2025-07-28
- 添加声望系统相关接口
- 添加随机事件处理接口
- 扩展成就系统接口

### 2025-07-25
- 实现基础的酒店管理接口
- 实现员工管理接口
- 实现部门管理接口
- 实现房间管理接口
- 实现财务管理接口
- 实现营销管理接口

## 7. 前端页面变更历史

### 2025-08-02
- 更新酒店升级页面，修改部门和房间解锁展示规则
- 更新首页，显示完整的部门和房间信息
- 优化界面布局和样式

### 2025-07-30
- 添加成就分页功能
- 添加奖励领取按钮
- 优化成就页面展示效果

### 2025-07-28
- 添加声望值和声望等级显示
- 添加随机事件提示功能
- 优化成就系统页面

### 2025-07-25
- 实现首页展示酒店基本信息和经营状况
- 实现员工管理页面
- 实现部门管理页面
- 实现房间管理页面
- 实现财务管理页面
- 实现营销管理页面
- 实现酒店升级页面

## 8. 部署说明

### 8.1 环境要求
- Python 3.8或更高版本
- pip包管理器
- 至少100MB磁盘空间

### 8.2 安装步骤
1. 克隆项目代码到本地
2. 安装依赖包：`pip install -r requirements.txt`
3. 初始化数据库：`python run.py`
4. 启动服务：`python run.py`
5. 访问应用：http://localhost:5000

### 8.3 配置说明
- 数据库文件存储在instance目录下
- 日志文件存储在logs目录下
- 静态文件存储在static目录下
- 模板文件存储在app/templates目录下
