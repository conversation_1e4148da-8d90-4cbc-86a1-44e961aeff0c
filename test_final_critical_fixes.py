#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终关键修复：满意度计算调整、按钮卡顿修复、Department变量错误修复
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_department_variable_fix():
    """测试Department变量错误修复"""
    print("🔧 测试Department变量错误修复")
    print("-" * 40)
    
    try:
        # 等待几秒让系统运行，检查是否还有Department错误
        time.sleep(5)
        
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("  ✅ 系统运行正常，无Department变量错误")
                return True
            else:
                print("  ❌ API返回失败")
                return False
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试Department变量时出错: {e}")
        return False

def test_satisfaction_calculation_adjustment():
    """测试满意度计算调整"""
    print("\n😊 测试满意度计算调整")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                level = data.get('level', 1)
                satisfaction = data.get('satisfaction', 0)
                
                print(f"  📊 当前酒店状态:")
                print(f"    - 酒店等级: {level}星")
                print(f"    - 满意度: {satisfaction:.1f}分")
                
                # 检查满意度是否能达到升级要求
                upgrade_requirements = {
                    1: 60,  # 1星升2星需要60分
                    2: 65,  # 2星升3星需要65分
                    3: 70,  # 3星升4星需要70分
                    4: 75,  # 4星升5星需要75分
                    5: 80,  # 5星升6星需要80分
                    6: 85,  # 6星升7星需要85分
                    7: 90,  # 7星升8星需要90分
                    8: 95   # 8星升9星需要95分
                }
                
                next_level_requirement = upgrade_requirements.get(level, 100)
                satisfaction_gap = next_level_requirement - satisfaction
                
                print(f"    - 下级要求: {next_level_requirement}分")
                print(f"    - 满意度差距: {satisfaction_gap:.1f}分")
                
                if satisfaction_gap <= 10:  # 差距在10分以内认为合理
                    print("  ✅ 满意度计算合理，能够达到升级要求")
                    return True
                else:
                    print("  ❌ 满意度计算仍需调整，差距过大")
                    return False
            else:
                print("  ❌ 获取酒店数据失败")
                return False
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试满意度计算时出错: {e}")
        return False

def test_button_responsiveness():
    """测试按钮响应性"""
    print("\n🖱️ 测试按钮响应性")
    print("-" * 40)
    
    try:
        # 测试主要页面的JavaScript加载
        pages_to_test = [
            ("/employees/management", "员工管理"),
            ("/rooms/management", "房间管理"),
            ("/marketing/management", "营销管理")
        ]
        
        js_functions_found = 0
        for url, name in pages_to_test:
            response = requests.get(f"{BASE_URL}{url}", timeout=10)
            if response.status_code == 200:
                content = response.text
                
                # 检查是否包含防重复点击功能
                if "disableButton" in content:
                    js_functions_found += 1
                    print(f"    ✅ {name}包含按钮防重复点击功能")
                else:
                    print(f"    ❌ {name}缺少按钮防重复点击功能")
            else:
                print(f"    ❌ {name}页面访问失败")
        
        # 检查base.html中的防抖功能
        base_response = requests.get(f"{BASE_URL}/", timeout=10)
        if base_response.status_code == 200:
            base_content = base_response.text
            if "debounce" in base_content and "disableButton" in base_content:
                print("  ✅ 基础防抖和按钮防重复功能已添加")
                js_functions_found += 1
            else:
                print("  ❌ 基础防抖功能缺失")
        
        if js_functions_found >= 2:
            print("  ✅ 按钮响应性优化完成")
            return True
        else:
            print("  ❌ 按钮响应性优化不完整")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试按钮响应性时出错: {e}")
        return False

def test_system_stability():
    """测试系统稳定性"""
    print("\n🔧 测试系统稳定性")
    print("-" * 40)
    
    try:
        # 测试主要页面访问
        pages = [
            ("/", "首页"),
            ("/employees/management", "员工管理"),
            ("/departments/management", "部门管理"),
            ("/rooms/management", "房间管理"),
            ("/marketing/management", "营销管理"),
            ("/hotel/management", "酒店升级")
        ]
        
        success_count = 0
        for url, name in pages:
            response = requests.get(f"{BASE_URL}{url}", timeout=10)
            if response.status_code == 200:
                success_count += 1
                print(f"    ✅ {name}访问正常")
            else:
                print(f"    ❌ {name}访问失败: {response.status_code}")
        
        print(f"  📊 页面访问成功率: {success_count}/{len(pages)}")
        
        # 测试API稳定性
        api_success = 0
        api_endpoints = [
            ("/api/hotel_info", "酒店信息"),
            ("/employees/get_employees_list", "员工列表")
        ]
        
        for url, name in api_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{url}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        api_success += 1
                        print(f"    ✅ {name}API正常")
                    else:
                        print(f"    ❌ {name}API返回失败")
                else:
                    print(f"    ❌ {name}API请求失败")
            except:
                print(f"    ❌ {name}API异常")
        
        print(f"  📊 API访问成功率: {api_success}/{len(api_endpoints)}")
        
        return success_count >= 5 and api_success >= 1
        
    except Exception as e:
        print(f"  ❌ 测试系统稳定性时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 最终关键修复验证测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项测试
    test_results = []
    test_results.append(("Department变量错误修复", test_department_variable_fix()))
    test_results.append(("满意度计算调整", test_satisfaction_calculation_adjustment()))
    test_results.append(("按钮响应性优化", test_button_responsiveness()))
    test_results.append(("系统稳定性", test_system_stability()))
    
    print("\n" + "=" * 60)
    print("📋 最终关键修复验证总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！最终关键修复全部完成")
        print("✅ Department变量: 删除重复导入，修复作用域问题")
        print("✅ 满意度计算: 调整基础分数和加成，确保能达到升级要求")
        print("✅ 按钮响应性: 添加防抖和防重复点击功能")
        print("✅ 系统稳定性: 所有页面和API正常工作")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    print("\n🎯 核心改进:")
    print("💡 满意度系统: 根据酒店等级调整基础分数，增加员工和部门加成")
    print("💡 按钮优化: 防抖和防重复点击，提升用户体验")
    print("💡 错误修复: 解决Department变量作用域问题")
    print("💡 系统稳定: 所有功能正常运行，无错误日志")
    
    print("\n🚀 可以通过浏览器访问查看效果:")
    print("   - 首页数据: http://127.0.0.1:5000")
    print("   - 员工管理: http://127.0.0.1:5000/employees/management")
    print("   - 酒店升级: http://127.0.0.1:5000/hotel/management")

if __name__ == "__main__":
    main()
