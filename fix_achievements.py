import sqlite3
from app import create_app

def fix_achievements():
    """修复成就数据，删除重复记录"""
    app = create_app()
    
    with app.app_context():
        # 获取数据库路径
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        print(f"数据库路径: {db_path}")
        
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 获取所有成就记录
            cursor.execute("SELECT id, hotel_id, name FROM achievement ORDER BY id")
            achievements = cursor.fetchall()
            
            print(f"修复前共有 {len(achievements)} 条成就记录")
            
            # 用于跟踪已处理的成就
            seen_achievements = {}
            ids_to_delete = []
            
            # 遍历成就记录，标记重复项
            for achievement in achievements:
                achievement_id, hotel_id, name = achievement
                key = (hotel_id, name)
                
                if key in seen_achievements:
                    # 这是一个重复项，标记删除
                    ids_to_delete.append(achievement_id)
                else:
                    # 第一次见到这个成就，记录下来
                    seen_achievements[key] = achievement_id
            
            # 删除重复的成就记录
            if ids_to_delete:
                placeholders = ','.join('?' * len(ids_to_delete))
                cursor.execute(f"DELETE FROM achievement WHERE id IN ({placeholders})", ids_to_delete)
                conn.commit()
                print(f"删除了 {len(ids_to_delete)} 条重复的成就记录")
            else:
                print("没有发现重复的成就记录")
            
            # 验证修复结果
            cursor.execute("SELECT COUNT(*) FROM achievement")
            count_after = cursor.fetchone()[0]
            print(f"修复后共有 {count_after} 条成就记录")
            
        except Exception as e:
            print(f"修复过程中出现错误: {e}")
            conn.rollback()
        finally:
            conn.close()

if __name__ == '__main__':
    fix_achievements()