#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
项目启动文件
包含应用初始化和启动逻辑
"""

import os
import sys
import atexit
from app import create_app, db
from app.models import initialize_game_data
from app.main.utils import stop_time_thread as utils_stop_time_thread

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def init_db():
    """初始化数据库"""
    app = create_app()
    with app.app_context():
        # 创建所有表
        db.create_all()
        
        # 检查是否需要初始化游戏数据
        from app.models import Hotel
        
        # 检查hotel表是否需要添加time_speed字段
        inspector = db.inspect(db.engine)
        columns = [column['name'] for column in inspector.get_columns('hotel')]
        
        if 'time_speed' not in columns:
            # 添加time_speed字段
            try:
                # 使用原始SQL添加字段
                with db.engine.connect() as conn:
                    conn.execute(db.text('ALTER TABLE hotel ADD COLUMN time_speed INTEGER DEFAULT 1'))
                    conn.commit()
                print("已添加time_speed字段到hotel表")
            except Exception as e:
                print(f"添加time_speed字段时出错: {e}")
        
        if not Hotel.query.first():
            initialize_game_data()
            print("数据库初始化完成")
        else:
            print("数据库已存在，跳过初始化")


def stop_time_thread():
    """停止时间推进线程"""
    utils_stop_time_thread()
    print("时间推进线程已停止")


def main():
    """主函数"""
    # 确保instance目录存在
    instance_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance')
    if not os.path.exists(instance_dir):
        os.makedirs(instance_dir)
        print(f"创建目录: {instance_dir}")
    
    # 初始化数据库
    init_db()
    
    # 创建应用并运行
    app = create_app()
    
    # 启动时间推进线程
    from app.main.utils import start_time_thread
    time_thread = start_time_thread()
    
    # 注册退出处理函数
    atexit.register(stop_time_thread)
    
    # 启动应用
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)

if __name__ == '__main__':
    main()