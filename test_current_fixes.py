#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试当前修复：营销活动、成就系统、员工等级、声望计算说明
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_marketing_campaigns():
    """测试营销活动数量"""
    print("📢 测试营销活动数量")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/marketing/management", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 营销管理页面访问正常")
            
            # 检查营销活动数量
            campaign_names = [
                "报纸杂志广告", "网络推广", "社交媒体营销", "电视广告", 
                "户外广告", "明星代言", "国际推广"
            ]
            
            found_campaigns = sum(1 for name in campaign_names if name in content)
            print(f"  📊 营销活动数量: {found_campaigns}/{len(campaign_names)}")
            
            if found_campaigns >= 3:
                print("  ✅ 营销活动数量正常（1星酒店可见多个活动）")
                return True
            else:
                print("  ❌ 营销活动数量过少")
                return False
                
        else:
            print(f"  ❌ 营销管理页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试营销活动时出错: {e}")
        return False

def test_achievements_system():
    """测试成就系统修复"""
    print("\n🏆 测试成就系统修复")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/management", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 成就管理页面访问正常")
            
            # 检查是否删除了达成要求字段
            if "达成要求" not in content:
                print("  ✅ 已删除达成要求字段")
            else:
                print("  ❌ 仍包含达成要求字段")
            
            # 检查完成度图标是否更换
            if "bi-pie-chart-fill" in content:
                print("  ✅ 完成度图标已更换为饼图")
            elif "bi-percent" in content:
                print("  ❌ 完成度图标仍为百分号")
            else:
                print("  ⚠️ 未找到完成度图标")
            
            # 获取成就数量
            api_response = requests.get(f"{BASE_URL}/achievements/get_achievements_list", timeout=10)
            if api_response.status_code == 200:
                data = api_response.json()
                if data.get('success'):
                    achievements = data.get('achievements', {})
                    total_achievements = sum(len(category_achievements) for category_achievements in achievements.values())
                    
                    print(f"  📊 成就统计:")
                    print(f"    - 总成就数: {total_achievements}")
                    
                    # 检查每类成就数量
                    for category, category_achievements in achievements.items():
                        count = len(category_achievements)
                        print(f"    - {category}: {count}个成就")
                        
                    return True
                else:
                    print(f"  ❌ 获取成就数据失败: {data.get('message', '')}")
                    return False
            else:
                print(f"  ❌ 成就API请求失败: {api_response.status_code}")
                return False
                
        else:
            print(f"  ❌ 成就管理页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试成就系统时出错: {e}")
        return False

def test_employee_level_display():
    """测试员工等级显示"""
    print("\n👥 测试员工等级显示")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/employees/management", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 员工管理页面访问正常")
            
            # 检查员工等级显示（不应该有多余的"级"字）
            level_patterns = ["初级级", "中级级", "高级级", "特级级"]
            found_extra_levels = sum(1 for pattern in level_patterns if pattern in content)
            
            if found_extra_levels == 0:
                print("  ✅ 员工等级显示正常，无多余的'级'字")
                return True
            else:
                print(f"  ❌ 发现{found_extra_levels}个多余的'级'字")
                return False
                
        else:
            print(f"  ❌ 员工管理页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试员工等级显示时出错: {e}")
        return False

def test_reputation_calculation_help():
    """测试声望计算说明"""
    print("\n🏅 测试声望计算说明")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 首页访问正常")
            
            # 检查声望说明按钮
            if "reputationHelpModal" in content:
                print("  ✅ 包含声望说明按钮")
            else:
                print("  ❌ 缺少声望说明按钮")
                
            # 检查声望说明模态框
            modal_elements = [
                "声望计算说明",
                "满意度等级影响",
                "财务表现等级影响",
                "计算示例"
            ]
            
            found_modal = sum(1 for element in modal_elements if element in content)
            print(f"  📋 声望说明内容: {found_modal}/{len(modal_elements)}")
            
            if found_modal >= 3:
                print("  ✅ 声望计算说明基本完整")
                return True
            else:
                print("  ❌ 声望计算说明不完整")
                return False
                
        else:
            print(f"  ❌ 首页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试声望计算说明时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 当前修复验证测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项测试
    test_results = []
    test_results.append(("营销活动数量修复", test_marketing_campaigns()))
    test_results.append(("成就系统优化", test_achievements_system()))
    test_results.append(("员工等级显示修复", test_employee_level_display()))
    test_results.append(("声望计算说明", test_reputation_calculation_help()))
    
    print("\n" + "=" * 60)
    print("📋 当前修复验证总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！当前修复全部完成")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    print("\n🚀 可以通过浏览器访问查看效果:")
    print("   - 营销管理: http://127.0.0.1:5000/marketing/management")
    print("   - 成就系统: http://127.0.0.1:5000/achievements/management")
    print("   - 员工管理: http://127.0.0.1:5000/employees/management")
    print("   - 声望说明: http://127.0.0.1:5000 (点击声望旁的问号)")

if __name__ == "__main__":
    main()
