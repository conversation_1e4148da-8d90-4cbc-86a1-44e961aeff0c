#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_homepage_display():
    """测试首页显示效果"""
    print('🎨 测试首页优化效果...')
    
    try:
        # 测试首页访问
        r = requests.get('http://127.0.0.1:5000/')
        if r.status_code == 200:
            print('✅ 首页访问: 正常')
            
            # 检查页面内容是否包含优化后的元素
            content = r.text
            
            # 检查游戏时间显示
            if 'currentDate' in content:
                print('✅ 游戏时间显示: 已包含')
            else:
                print('❌ 游戏时间显示: 缺失')
            
            # 检查优化后的样式
            if 'font-size: 1rem' in content:
                print('✅ 字体大小优化: 已应用')
            else:
                print('❌ 字体大小优化: 未应用')
                
            if 'py-2 px-3' in content:
                print('✅ 紧凑布局: 已应用')
            else:
                print('❌ 紧凑布局: 未应用')
                
            return True
        else:
            print(f'❌ 首页访问: HTTP {r.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ 首页测试失败: {e}')
        return False

def test_hotel_info_api():
    """测试酒店信息API"""
    print('\n🏨 测试酒店信息API...')
    
    try:
        r = requests.get('http://127.0.0.1:5000/api/hotel_info')
        if r.status_code == 200:
            data = r.json()
            print('✅ 酒店信息API: 正常')
            
            # 检查关键数据
            required_fields = ['name', 'level', 'money', 'satisfaction', 'reputation', 'date', 'days_elapsed']
            missing_fields = []
            
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
                else:
                    print(f'   ✅ {field}: {data[field]}')
            
            if missing_fields:
                print(f'   ❌ 缺失字段: {missing_fields}')
                return False
            else:
                print('   ✅ 所有必需字段都存在')
                return True
                
        else:
            print(f'❌ 酒店信息API: HTTP {r.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ 酒店信息API测试失败: {e}')
        return False

def test_occupancy_chart():
    """测试入住率图表数据"""
    print('\n📊 测试入住率图表数据...')
    
    try:
        r = requests.get('http://127.0.0.1:5000/api/occupancy_data')
        if r.status_code == 200:
            data = r.json()
            print('✅ 入住率数据API: 正常')
            
            if isinstance(data, dict) and len(data) > 0:
                for room_type, occupancy_list in data.items():
                    if isinstance(occupancy_list, list):
                        print(f'   ✅ {room_type}: {len(occupancy_list)}天数据')
                        
                        # 检查数据格式
                        if len(occupancy_list) > 0:
                            sample = occupancy_list[0]
                            if 'date' in sample and 'rate' in sample:
                                print(f'      数据格式正确: {sample}')
                            else:
                                print(f'      数据格式错误: {sample}')
                    else:
                        print(f'   ❌ {room_type}: 数据格式错误')
                        
                return True
            else:
                print('❌ 入住率数据格式错误')
                return False
                
        else:
            print(f'❌ 入住率数据API: HTTP {r.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ 入住率图表测试失败: {e}')
        return False

def test_all_list_pages():
    """测试所有列表样式页面"""
    print('\n📋 测试列表样式页面...')
    
    pages = [
        ('部门管理', '/departments/management'),
        ('房间管理', '/rooms/management'),
        ('营销管理', '/marketing/management'),
        ('酒店升级', '/hotel/management'),
    ]
    
    success_count = 0
    for name, url in pages:
        try:
            r = requests.get(f'http://127.0.0.1:5000{url}')
            if r.status_code == 200:
                print(f'   ✅ {name}: 正常')
                success_count += 1
            else:
                print(f'   ❌ {name}: HTTP {r.status_code}')
        except Exception as e:
            print(f'   ❌ {name}: 异常 - {e}')
    
    print(f'   列表页面测试结果: {success_count}/{len(pages)} 成功')
    return success_count == len(pages)

if __name__ == '__main__':
    print('🚀 开始首页优化效果测试...')
    
    homepage_ok = test_homepage_display()
    api_ok = test_hotel_info_api()
    chart_ok = test_occupancy_chart()
    pages_ok = test_all_list_pages()
    
    print('\n📊 测试结果总结:')
    print(f'✅ 首页显示: {"通过" if homepage_ok else "失败"}')
    print(f'✅ 酒店信息API: {"通过" if api_ok else "失败"}')
    print(f'✅ 入住率图表: {"通过" if chart_ok else "失败"}')
    print(f'✅ 列表样式页面: {"通过" if pages_ok else "失败"}')
    
    if homepage_ok and api_ok and chart_ok and pages_ok:
        print('\n🎉 首页优化完成！界面更加紧凑美观！')
        print('💡 优化效果:')
        print('   - 数据显示更加紧凑，字体大小适中')
        print('   - 图标和按钮尺寸优化，更加精致')
        print('   - 减少了空白区域，布局更紧凑')
        print('   - 游戏时间正常显示')
        print('   - 入住率图表显示当月完整数据')
        print('   - 所有管理页面统一为列表样式')
    else:
        print('\n⚠️ 部分功能还有问题，需要进一步调试。')
