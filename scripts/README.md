# 脚本目录说明

本目录包含用于维护和管理酒店管理系统的各种脚本工具。

## 脚本列表

### [check_game_rules.py](file:///D:/myCode/hotel-management-system/scripts/check_game_rules.py)
用于检查数据库中的所有游戏规则设置，验证规则是否完整存在，并生成报告文件。

功能：
- 检查所有预期的游戏规则是否都存在于数据库中
- 显示每个规则的详细信息和内容项数
- 生成易于阅读的报告文件 [rules_report.txt](file:///D:/myCode/hotel-management-system/scripts/rules_report.txt)
- 使用中文说明展示规则名称，便于理解

使用方法：
```bash
python scripts/check_game_rules.py
```

### [init_game_rules.py](file:///D:/myCode/hotel-management-system/scripts/init_game_rules.py)
用于初始化数据库中的所有游戏规则，基于需求文档设置完整的规则体系。

功能：
- 初始化所有游戏规则到数据库
- 包括部门解锁规则、酒店升级规则、员工等级规则等
- 支持更新已存在的规则
- 提供详细的操作日志

使用方法：
```bash
python scripts/init_game_rules.py
```

## 使用说明

1. 所有脚本均可独立运行，不需要启动Web服务
2. 脚本会自动连接到项目配置的数据库
3. 建议在运行任何脚本前先备份数据库
4. 脚本运行结果会输出到控制台，并在适当情况下生成报告文件

## 注意事项

- 确保在项目根目录下运行脚本
- 确保数据库服务正在运行且可访问
- 脚本会根据需要自动创建缺失的规则，但不会删除已存在的规则