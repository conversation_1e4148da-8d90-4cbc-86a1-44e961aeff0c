#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库优化脚本 - 添加索引和优化配置
"""

from app import create_app, db
from sqlalchemy import text

def optimize_database():
    """优化数据库性能"""
    app = create_app()
    with app.app_context():
        print('🔧 开始数据库优化...')
        
        # 1. 配置SQLite为高并发模式
        print('📝 配置SQLite并发模式...')
        optimizations = [
            "PRAGMA journal_mode=WAL",           # WAL模式，支持并发读写
            "PRAGMA synchronous=NORMAL",         # 正常同步模式，平衡性能和安全
            "PRAGMA cache_size=10000",           # 增大缓存
            "PRAGMA temp_store=memory",          # 临时表存储在内存
            "PRAGMA mmap_size=268435456",        # 256MB内存映射
            "PRAGMA busy_timeout=30000",         # 30秒忙等超时
            "PRAGMA wal_autocheckpoint=1000",    # WAL自动检查点
            "PRAGMA optimize"                    # 优化查询计划
        ]
        
        for sql in optimizations:
            try:
                db.session.execute(text(sql))
                print(f'✅ {sql}')
            except Exception as e:
                print(f'❌ {sql} - 错误: {e}')
        
        # 2. 添加关键索引
        print('\n📊 添加数据库索引...')
        indexes = [
            # Hotel表索引
            "CREATE INDEX IF NOT EXISTS idx_hotel_date ON hotel(date)",
            
            # Room表索引
            "CREATE INDEX IF NOT EXISTS idx_room_hotel_id ON room(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_room_type ON room(type)",
            "CREATE INDEX IF NOT EXISTS idx_room_hotel_type ON room(hotel_id, type)",
            
            # Employee表索引
            "CREATE INDEX IF NOT EXISTS idx_employee_hotel_id ON employee(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_employee_department ON employee(department)",
            "CREATE INDEX IF NOT EXISTS idx_employee_hotel_dept ON employee(hotel_id, department)",
            
            # Department表索引
            "CREATE INDEX IF NOT EXISTS idx_department_hotel_id ON department(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_department_unlocked ON department(hotel_id, is_unlocked)",
            
            # FinancialRecord表索引（最重要，每日都会查询）
            "CREATE INDEX IF NOT EXISTS idx_financial_hotel_id ON financial_record(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_financial_date ON financial_record(record_date)",
            "CREATE INDEX IF NOT EXISTS idx_financial_hotel_date ON financial_record(hotel_id, record_date)",
            "CREATE INDEX IF NOT EXISTS idx_financial_hotel_date_desc ON financial_record(hotel_id, record_date DESC)",
            
            # Achievement表索引
            "CREATE INDEX IF NOT EXISTS idx_achievement_hotel_id ON achievement(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_achievement_achieved ON achievement(hotel_id, achieved)",
            
            # RandomEvent表索引
            "CREATE INDEX IF NOT EXISTS idx_random_event_hotel_id ON random_event(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_random_event_active ON random_event(hotel_id, is_active)",
            
            # MarketingCampaign表索引
            "CREATE INDEX IF NOT EXISTS idx_marketing_hotel_id ON marketing_campaign(hotel_id)",
            "CREATE INDEX IF NOT EXISTS idx_marketing_active ON marketing_campaign(hotel_id, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_marketing_dates ON marketing_campaign(started_at, ends_at)"
        ]
        
        for sql in indexes:
            try:
                db.session.execute(text(sql))
                print(f'✅ {sql.split()[-1]}')  # 显示索引名
            except Exception as e:
                print(f'❌ {sql.split()[-1]} - 错误: {e}')
        
        # 3. 提交所有更改
        try:
            db.session.commit()
            print('\n✅ 数据库优化完成！')
        except Exception as e:
            print(f'\n❌ 提交失败: {e}')
            db.session.rollback()
        
        # 4. 验证优化结果
        print('\n📊 验证优化结果...')
        
        # 检查Journal模式
        result = db.session.execute(text('PRAGMA journal_mode'))
        journal_mode = result.fetchone()[0]
        print(f'Journal模式: {journal_mode}')
        
        # 检查索引数量
        result = db.session.execute(text('SELECT COUNT(*) FROM sqlite_master WHERE type="index" AND name NOT LIKE "sqlite_%"'))
        index_count = result.fetchone()[0]
        print(f'用户索引数量: {index_count}')
        
        # 检查数据库大小
        result = db.session.execute(text('PRAGMA page_count'))
        page_count = result.fetchone()[0]
        result = db.session.execute(text('PRAGMA page_size'))
        page_size = result.fetchone()[0]
        db_size = page_count * page_size / 1024 / 1024  # MB
        print(f'数据库大小: {db_size:.2f} MB')

def init_basic_data():
    """初始化基础数据"""
    app = create_app()
    with app.app_context():
        from app.models import Hotel, Room, Department
        
        print('\n🏨 检查基础数据...')
        
        hotel = Hotel.query.first()
        if not hotel:
            print('❌ 没有酒店数据')
            return
            
        # 检查房间数据
        room_count = Room.query.filter_by(hotel_id=hotel.id).count()
        if room_count == 0:
            print('🏠 初始化房间数据...')
            
            # 添加基础房间
            basic_rooms = [
                {'type': '标准间', 'count': 10, 'price': 200},
                {'type': '豪华间', 'count': 5, 'price': 400},
                {'type': '商务套房', 'count': 2, 'price': 800}
            ]
            
            for room_data in basic_rooms:
                room = Room(
                    hotel_id=hotel.id,
                    type=room_data['type'],
                    count=room_data['count'],
                    price=room_data['price']
                )
                db.session.add(room)
            
            try:
                db.session.commit()
                print('✅ 基础房间数据初始化完成')
            except Exception as e:
                print(f'❌ 房间数据初始化失败: {e}')
                db.session.rollback()
        else:
            print(f'✅ 房间数据正常 ({room_count}种房间类型)')
        
        # 检查部门数据
        dept_count = Department.query.filter_by(hotel_id=hotel.id).count()
        if dept_count == 0:
            print('🏢 初始化部门数据...')
            
            departments = [
                {'name': '前台', 'is_unlocked': True},
                {'name': '客房部', 'is_unlocked': True},
                {'name': '餐饮部', 'is_unlocked': False},
                {'name': '财务部', 'is_unlocked': False},
                {'name': '工程部', 'is_unlocked': False},
                {'name': '安保部', 'is_unlocked': False}
            ]
            
            for dept_data in departments:
                dept = Department(
                    hotel_id=hotel.id,
                    name=dept_data['name'],
                    is_unlocked=dept_data['is_unlocked']
                )
                db.session.add(dept)
            
            try:
                db.session.commit()
                print('✅ 基础部门数据初始化完成')
            except Exception as e:
                print(f'❌ 部门数据初始化失败: {e}')
                db.session.rollback()
        else:
            print(f'✅ 部门数据正常 ({dept_count}个部门)')

if __name__ == '__main__':
    print('🚀 开始数据库根本性优化...')
    optimize_database()
    init_basic_data()
    print('\n🎉 优化完成！数据库性能应该显著提升。')
