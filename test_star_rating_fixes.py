#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试星级显示和计算说明修复
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_hotel_upgrade_conditions():
    """测试酒店升级条件判断"""
    print("🏨 测试酒店升级条件判断")
    print("-" * 40)
    
    try:
        # 获取当前酒店状态
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                level = data.get('level', 1)
                money = data.get('money', 0)
                reputation = data.get('reputation', 0)
                satisfaction = data.get('satisfaction', 0)
                days_elapsed = data.get('days_elapsed', 0)
                
                print(f"  📊 当前酒店状态:")
                print(f"    - 星级: {level}星")
                print(f"    - 资金: ¥{money:,}")
                print(f"    - 声望: {reputation}")
                print(f"    - 满意度: {satisfaction:.1f}分")
                print(f"    - 运营天数: {days_elapsed}天")
                
                # 检查升级页面
                upgrade_response = requests.get(f"{BASE_URL}/hotel/management", timeout=10)
                if upgrade_response.status_code == 200:
                    content = upgrade_response.text
                    print("  ✅ 酒店升级页面访问正常")
                    
                    # 检查是否有调试信息（title属性）
                    if 'title="资金:' in content:
                        print("  ✅ 包含升级条件调试信息")
                    else:
                        print("  ❌ 缺少升级条件调试信息")
                    
                    # 检查升级按钮状态
                    if "btn-success" in content and "升级" in content:
                        print("  ✅ 显示绿色升级按钮（条件满足）")
                        return True
                    elif "条件不足" in content:
                        print("  ⚠️ 显示条件不足（可能正常）")
                        return True
                    else:
                        print("  ❌ 升级按钮状态异常")
                        return False
                        
                else:
                    print(f"  ❌ 升级页面访问失败: {upgrade_response.status_code}")
                    return False
            else:
                print(f"  ❌ 获取酒店状态失败: {data.get('message', '')}")
                return False
        else:
            print(f"  ❌ 酒店状态请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试升级条件时出错: {e}")
        return False

def test_star_rating_display():
    """测试星级显示"""
    print("\n⭐ 测试星级显示")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 首页访问正常")
            
            # 检查星级显示元素
            star_elements = [
                "酒店星级",  # 标题改为星级
                "bi-star-fill",  # 实心星星
                "bi-star text-muted",  # 空心星星
                "星级酒店"  # 描述文字
            ]
            
            found_elements = sum(1 for element in star_elements if element in content)
            print(f"  ⭐ 星级显示元素: {found_elements}/{len(star_elements)}")
            
            # 检查JavaScript星级更新函数
            if "starsHtml" in content and "bi-star-fill" in content:
                print("  ✅ 包含JavaScript星级更新函数")
            else:
                print("  ❌ 缺少JavaScript星级更新函数")
            
            # 检查升级页面星级显示
            upgrade_response = requests.get(f"{BASE_URL}/hotel/management", timeout=10)
            if upgrade_response.status_code == 200:
                upgrade_content = upgrade_response.text
                if "bi-star-fill" in upgrade_content and "星级" in upgrade_content:
                    print("  ✅ 升级页面星级显示正常")
                else:
                    print("  ❌ 升级页面星级显示异常")
            
            if found_elements >= 3:
                print("  ✅ 星级显示修复完成")
                return True
            else:
                print("  ❌ 星级显示修复不完整")
                return False
                
        else:
            print(f"  ❌ 首页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试星级显示时出错: {e}")
        return False

def test_calculation_help_icons():
    """测试计算说明小图标"""
    print("\n🧮 测试计算说明小图标")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 首页访问正常")
            
            # 检查计算说明图标
            help_modals = [
                "financeHelpModal",  # 资金计算说明
                "satisfactionHelpModal",  # 满意度计算说明
                "reputationHelpModal"  # 声望计算说明
            ]
            
            found_modals = sum(1 for modal in help_modals if modal in content)
            print(f"  🔍 计算说明模态框: {found_modals}/{len(help_modals)}")
            
            # 检查问号图标按钮
            help_buttons = content.count('data-bs-toggle="modal"')
            print(f"  🔘 计算说明按钮: {help_buttons}个")
            
            # 检查具体的计算说明内容
            calculation_contents = [
                "资金计算说明",
                "满意度计算说明", 
                "声望计算说明",
                "收入来源",
                "支出项目",
                "正面因素",
                "负面因素"
            ]
            
            found_contents = sum(1 for content_item in calculation_contents if content_item in content)
            print(f"  📋 计算说明内容: {found_contents}/{len(calculation_contents)}")
            
            if found_modals >= 2 and help_buttons >= 3 and found_contents >= 5:
                print("  ✅ 计算说明功能完整")
                return True
            else:
                print("  ❌ 计算说明功能不完整")
                return False
                
        else:
            print(f"  ❌ 首页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试计算说明时出错: {e}")
        return False

def test_system_stability():
    """测试系统稳定性"""
    print("\n🔧 测试系统稳定性")
    print("-" * 40)
    
    try:
        # 测试主要页面访问
        pages = [
            ("/", "首页"),
            ("/hotel/management", "酒店升级"),
            ("/rooms/management", "房间管理"),
            ("/employees/management", "员工管理")
        ]
        
        success_count = 0
        for url, name in pages:
            response = requests.get(f"{BASE_URL}{url}", timeout=10)
            if response.status_code == 200:
                success_count += 1
                print(f"    ✅ {name}访问正常")
            else:
                print(f"    ❌ {name}访问失败: {response.status_code}")
        
        print(f"  📊 页面访问成功率: {success_count}/{len(pages)}")
        
        return success_count >= 3
        
    except Exception as e:
        print(f"  ❌ 测试系统稳定性时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 星级显示和计算说明修复验证")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项测试
    test_results = []
    test_results.append(("酒店升级条件判断", test_hotel_upgrade_conditions()))
    test_results.append(("星级显示修复", test_star_rating_display()))
    test_results.append(("计算说明小图标", test_calculation_help_icons()))
    test_results.append(("系统稳定性", test_system_stability()))
    
    print("\n" + "=" * 60)
    print("📋 修复验证总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！星级显示和计算说明修复完成")
        print("✅ 酒店升级: 条件判断准确，包含调试信息")
        print("✅ 星级显示: 等级改为星级，用星星图标表示")
        print("✅ 计算说明: 所有数值都有计算说明小图标")
        print("✅ 系统稳定: 所有页面正常访问")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    print("\n🎯 核心改进:")
    print("⭐ 星级显示: 1-9星用实心/空心星星表示")
    print("🧮 计算说明: 资金、满意度、声望都有详细说明")
    print("🔍 升级调试: 鼠标悬停显示具体条件检查结果")
    print("📱 用户体验: 界面更加直观，信息更加透明")
    
    print("\n🚀 可以通过浏览器访问查看效果:")
    print("   - 首页星级: http://127.0.0.1:5000")
    print("   - 酒店升级: http://127.0.0.1:5000/hotel/management")
    print("   - 计算说明: 点击各数值旁的问号图标")

if __name__ == "__main__":
    main()
