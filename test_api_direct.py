#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试API端点
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_department_hire_api():
    """测试按部门招聘API"""
    print("🧪 直接测试按部门招聘API")
    print("-" * 40)

    # 等待服务器启动
    time.sleep(3)

    try:
        # 测试通过现有hire API进行按部门招聘
        url = f"{BASE_URL}/employees/hire"
        data = {"department": "前台部", "level": "初级"}

        print(f"📡 请求URL: {url}")
        print(f"📦 请求数据: {json.dumps(data, ensure_ascii=False)}")

        response = requests.post(url, json=data, timeout=10)

        print(f"📊 响应状态: {response.status_code}")
        print(f"📄 响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ API调用成功: {result.get('message', '')}")
            else:
                print(f"❌ API返回失败: {result.get('message', '')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_marketing_campaign():
    """测试营销活动API"""
    print("\n🧪 直接测试营销活动API")
    print("-" * 40)
    
    try:
        # 1. 获取营销活动列表
        url = f"{BASE_URL}/marketing/get_campaigns_list"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            campaigns = data.get("campaigns", [])
            print(f"📊 获取到 {len(campaigns)} 个营销活动")
            
            for campaign in campaigns:
                print(f"  {campaign['name']}: {'活跃' if campaign.get('is_active') else '可用'} (剩余{campaign.get('remaining_days', 0)}天)")
        
        # 2. 尝试启动营销活动
        url = f"{BASE_URL}/marketing/start_campaign"
        data = {"campaign_id": "print_ads"}
        
        print(f"\n📡 启动营销活动: {data}")
        response = requests.post(url, json=data, timeout=10)
        
        print(f"📊 响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"📄 错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主测试函数"""
    print("🔧 直接API测试")
    print("=" * 60)
    
    test_department_hire_api()
    test_marketing_campaign()

if __name__ == "__main__":
    main()
