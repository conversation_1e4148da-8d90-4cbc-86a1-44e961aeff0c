[{"test_name": "页面访问 - 首页", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 员工管理", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 部门管理", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 房间管理", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 酒店升级", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 营销管理", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 财务管理", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "页面访问 - 成就系统", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:36"}, {"test_name": "酒店基本信息字段", "status": "PASS", "details": "所有必需字段都存在: 红珊瑚大酒店, 1星", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "时间控制 - 切换时间状态", "status": "PASS", "details": "API响应正常", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "时间控制 - 切换时间速度", "status": "PASS", "details": "API响应正常", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "员工候选人列表", "status": "PASS", "details": "获取到 6 名候选人", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "员工招聘功能", "status": "PASS", "details": "招聘API响应正常", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "部门列表获取", "status": "PASS", "details": "获取到 11 个部门", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "部门解锁状态", "status": "PASS", "details": "3 个部门已解锁", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "房间列表获取", "status": "PASS", "details": "获取到 2 种房间类型", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "房间建设功能", "status": "PASS", "details": "建设API响应正常", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "营销活动列表", "status": "PASS", "details": "获取营销活动成功", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "财务记录获取", "status": "PASS", "details": "获取到 0 条财务记录", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "成就系统", "status": "PASS", "details": "获取到 5 个成就", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "页面访问 - 酒店升级页面", "status": "PASS", "details": "页面正常加载", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "酒店升级信息", "status": "PASS", "details": "升级页面正常访问", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "满意度计算", "status": "PASS", "details": "满意度: 61.0", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "声望值计算", "status": "PASS", "details": "声望值: 0", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "员工解雇功能", "status": "PASS", "details": "解雇功能正常", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "部门解锁功能", "status": "FAIL", "details": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "房间价格设置", "status": "FAIL", "details": "HTTP 404: <!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "营销活动启动", "status": "FAIL", "details": "HTTP 400: {\n  \"message\": \"\\u65e0\\u6548\\u7684\\u8425\\u9500\\u6d3b\\u52a8\",\n  \"success\": false\n}\n", "timestamp": "2025-08-06 22:51:37"}, {"test_name": "游戏存档功能", "status": "PASS", "details": "存档功能正常", "timestamp": "2025-08-06 22:51:37"}]