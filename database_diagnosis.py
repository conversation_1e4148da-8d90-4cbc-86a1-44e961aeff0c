#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import create_app, db
from app.models import Hotel, Room, Employee, FinancialRecord
from sqlalchemy import text

app = create_app()
with app.app_context():
    print('=== 数据库诊断 ===')

    # 检查表结构和索引
    result = db.session.execute(text('SELECT name FROM sqlite_master WHERE type="table"'))
    tables = [row[0] for row in result.fetchall()]
    print(f'数据库表: {tables}')

    # 检查索引
    for table in tables:
        if not table.startswith('sqlite_'):
            result = db.session.execute(text(f'PRAGMA index_list({table})'))
            indexes = result.fetchall()
            print(f'{table}表索引: {len(indexes)}个')
            for idx in indexes:
                print(f'  - {idx[1]} (unique: {idx[2]})')

    # 检查数据量
    hotel_count = Hotel.query.count()
    room_count = Room.query.count()
    employee_count = Employee.query.count()
    financial_count = FinancialRecord.query.count()

    print(f'\n=== 数据量统计 ===')
    print(f'酒店: {hotel_count}')
    print(f'房间: {room_count}')
    print(f'员工: {employee_count}')
    print(f'财务记录: {financial_count}')

    # 检查SQLite配置
    result = db.session.execute(text('PRAGMA journal_mode'))
    journal_mode = result.fetchone()[0]

    result = db.session.execute(text('PRAGMA synchronous'))
    synchronous = result.fetchone()[0]

    result = db.session.execute(text('PRAGMA busy_timeout'))
    busy_timeout = result.fetchone()[0]

    print(f'\n=== SQLite配置 ===')
    print(f'Journal模式: {journal_mode}')
    print(f'同步模式: {synchronous}')
    print(f'忙等超时: {busy_timeout}ms')

    # 检查是否有长时间运行的事务
    result = db.session.execute(text('SELECT * FROM sqlite_master WHERE type="index"'))
    all_indexes = result.fetchall()
    print(f'\n=== 所有索引 ===')
    for idx in all_indexes:
        print(f'{idx[1]} on {idx[2]}')
