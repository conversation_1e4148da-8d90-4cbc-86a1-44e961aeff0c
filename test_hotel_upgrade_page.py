#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试酒店升级页面
"""

import requests
import time

BASE_URL = "http://127.0.0.1:5000"

def test_hotel_upgrade_page():
    """测试酒店升级页面访问"""
    print("🏨 测试酒店升级页面访问")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/hotel/management", timeout=10)
        print(f"  📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ 酒店升级页面访问正常")
            content = response.text
            
            # 检查页面内容
            if "升级列表" in content:
                print("  ✅ 包含升级列表")
            elif "升级路径" in content:
                print("  ✅ 包含升级路径")
            else:
                print("  ❌ 缺少升级相关内容")
                
        elif response.status_code == 500:
            print("  ❌ 服务器内部错误 (500)")
            print(f"  📝 错误内容: {response.text[:200]}...")
        else:
            print(f"  ❌ 页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 测试酒店升级页面时出错: {e}")

if __name__ == "__main__":
    print("🔧 酒店升级页面测试")
    print("=" * 40)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    test_hotel_upgrade_page()
