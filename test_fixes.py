#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复的问题
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def api_call(endpoint, method="GET", data=None):
    """API调用封装"""
    try:
        url = f"{BASE_URL}{endpoint}"
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            return True, response.json() if response.content else {}
        else:
            return False, f"HTTP {response.status_code}: {response.text}"
    except Exception as e:
        return False, str(e)

def test_marketing_reuse():
    """测试营销活动重复使用"""
    print("🧪 测试营销活动重复使用")
    print("-" * 40)
    
    # 1. 查看当前营销活动状态
    success, data = api_call("/marketing/get_campaigns_list")
    if success:
        campaigns = data.get("campaigns", [])
        active_count = sum(1 for c in campaigns if c.get('is_active'))
        print(f"  📊 当前活跃营销活动: {active_count}")
        
        # 显示所有活动状态
        for campaign in campaigns:
            status = "活跃" if campaign.get('is_active') else "可用"
            remaining = campaign.get('remaining_days', 0)
            print(f"    {campaign['name']}: {status} (剩余{remaining}天)")
    
    # 2. 尝试启动一个营销活动
    print(f"\n  🚀 尝试启动营销活动...")
    success, data = api_call("/marketing/start_campaign", "POST", {"campaign_id": "print_ads"})
    if success:
        print(f"    ✅ 营销活动启动成功: {data.get('message', '')}")
    else:
        print(f"    ⚠️ 营销活动启动: {data}")

def test_room_building():
    """测试房间建设数量更新"""
    print("\n🧪 测试房间建设数量更新")
    print("-" * 40)
    
    # 1. 查看建设前的房间数量
    success, data = api_call("/rooms/get_rooms_list")
    if success:
        rooms = data.get("rooms", [])
        single_room = next((r for r in rooms if r['type'] == '单人间'), None)
        if single_room:
            before_count = single_room['count']
            print(f"  📊 建设前单人间数量: {before_count}")
            
            # 2. 建设2间单人间
            print(f"  🔨 建设2间单人间...")
            success, data = api_call("/rooms/build", "POST", {"room_type": "单人间", "quantity": 2})
            if success:
                print(f"    ✅ 建设成功: {data.get('message', '')}")
                
                # 3. 查看建设后的数量
                time.sleep(1)  # 等待数据更新
                success, data = api_call("/rooms/get_rooms_list")
                if success:
                    rooms = data.get("rooms", [])
                    single_room = next((r for r in rooms if r['type'] == '单人间'), None)
                    if single_room:
                        after_count = single_room['count']
                        print(f"  📊 建设后单人间数量: {after_count}")
                        if after_count > before_count:
                            print(f"    ✅ 数量更新正确: +{after_count - before_count}")
                        else:
                            print(f"    ❌ 数量未更新")
            else:
                print(f"    ❌ 建设失败: {data}")

def test_employee_hiring():
    """测试员工招聘系统"""
    print("\n🧪 测试员工招聘系统")
    print("-" * 40)
    
    # 1. 测试按部门招聘
    print(f"  👥 测试按部门招聘...")
    hire_data = {"department": "前台部", "level": "初级"}
    success, data = api_call("/employees/hire_by_department", "POST", hire_data)
    if success:
        print(f"    ✅ 按部门招聘成功: {data.get('message', '')}")
    else:
        print(f"    ❌ 按部门招聘失败: {data}")

    # 2. 测试候选人招聘（使用原有API）
    print(f"  👤 测试候选人招聘...")
    success, data = api_call("/employees/get_candidates_list")
    if success:
        candidates = data.get("candidates", [])
        if candidates:
            hire_data = {"candidate_id": candidates[0]["id"]}
            success, data = api_call("/employees/hire", "POST", hire_data)  # 使用原有API
            if success:
                print(f"    ✅ 候选人招聘成功: {data.get('message', '')}")
            else:
                print(f"    ❌ 候选人招聘失败: {data}")
        else:
            print(f"    ⚠️ 没有可用候选人")

def test_time_system():
    """测试时间系统"""
    print("\n🧪 测试时间系统")
    print("-" * 40)
    
    # 1. 查看当前时间状态
    success, data = api_call("/api/hotel_info")
    if success:
        print(f"  📅 当前时间: {data.get('current_date')}")
        print(f"  ⏰ 时间运行状态: {'运行中' if data.get('time_running') else '已暂停'}")
        print(f"  ⚡ 时间速度: {data.get('time_speed', 1)}倍速")
    
    # 2. 测试时间控制
    print(f"  🎮 测试时间控制...")
    success, data = api_call("/api/toggle_time", "POST")
    if success:
        print(f"    ✅ 时间控制成功: {data.get('message', '')}")
    else:
        print(f"    ❌ 时间控制失败: {data}")

def test_occupancy_stability():
    """测试入住率稳定性"""
    print("\n🧪 测试入住率稳定性")
    print("-" * 40)
    
    # 多次获取入住率数据，检查波动范围
    occupancy_data = []
    for i in range(5):
        success, data = api_call("/rooms/get_rooms_list")
        if success:
            rooms = data.get("rooms", [])
            single_room = next((r for r in rooms if r['type'] == '单人间'), None)
            if single_room:
                occupancy_data.append(single_room['occupancy_rate'])
        time.sleep(0.5)
    
    if occupancy_data:
        avg_rate = sum(occupancy_data) / len(occupancy_data)
        max_rate = max(occupancy_data)
        min_rate = min(occupancy_data)
        variation = max_rate - min_rate
        
        print(f"  📊 单人间入住率统计 (5次采样):")
        print(f"    平均值: {avg_rate:.1f}%")
        print(f"    最高值: {max_rate:.1f}%")
        print(f"    最低值: {min_rate:.1f}%")
        print(f"    波动范围: {variation:.1f}%")
        
        if variation <= 5:
            print(f"    ✅ 入住率稳定 (波动≤5%)")
        else:
            print(f"    ⚠️ 入住率波动较大 (波动>{variation:.1f}%)")

def test_restart_game():
    """测试重新开始游戏"""
    print("\n🧪 测试重新开始游戏")
    print("-" * 40)
    
    # 1. 记录重启前状态
    success, data = api_call("/api/hotel_info")
    if success:
        before_date = data.get('current_date')
        before_money = data.get('money')
        print(f"  📊 重启前状态: {before_date}, ¥{before_money:,}")
    
    # 2. 重新开始游戏
    print(f"  🔄 执行重新开始...")
    success, data = api_call("/api/restart_game", "POST")
    if success:
        print(f"    ✅ 重新开始成功: {data.get('message', '')}")
        
        # 3. 等待并检查重启后状态
        time.sleep(3)
        success, data = api_call("/api/hotel_info")
        if success:
            after_date = data.get('current_date')
            after_money = data.get('money')
            time_running = data.get('time_running')
            print(f"  📊 重启后状态: {after_date}, ¥{after_money:,}")
            print(f"  ⏰ 时间运行状态: {'运行中' if time_running else '已暂停'}")
            
            if time_running:
                print(f"    ✅ 时间系统正常运行")
            else:
                print(f"    ❌ 时间系统未启动")
    else:
        print(f"    ❌ 重新开始失败: {data}")

def main():
    """主测试函数"""
    print("🔧 问题修复验证测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 等待服务器启动
    print("\n⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项修复测试
    test_marketing_reuse()
    test_room_building()
    test_employee_hiring()
    test_time_system()
    test_occupancy_stability()
    test_restart_game()
    
    print("\n" + "=" * 60)
    print("🎯 修复验证完成")
    print("💡 请通过浏览器访问 http://127.0.0.1:5000 进一步验证")

if __name__ == "__main__":
    main()
