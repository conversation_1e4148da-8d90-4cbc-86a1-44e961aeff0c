#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import logging.handlers
import os
import json
import atexit
from datetime import datetime, date, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import func
from config import Config

# 从app包导入模型和数据库实例
from app import db, create_app
from app.models import Hotel, Room, Employee, Department, FinancialRecord, GameSetting

# 创建应用实例
app = create_app()

def stop_time_thread():
    """停止时间推进线程"""
    from app.main.utils import TIME_RUNNING
    TIME_RUNNING = False
    print("时间推进线程已停止")

if __name__ == '__main__':
    # 初始化酒店数据
    with app.app_context():
        # 确保数据库表已创建
        db.create_all()
        
        # 启动时间推进线程
        from app.main.utils import start_time_thread
        time_thread = start_time_thread()
        
        # 注册退出处理函数
        atexit.register(stop_time_thread)
        
        # 运行应用
        app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)