#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试成就奖励修复：金钱和声望奖励
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_achievement_rewards_display():
    """测试成就奖励显示"""
    print("🏆 测试成就奖励显示")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/management", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 成就管理页面访问正常")
            
            # 检查是否显示金钱奖励
            if "¥" in content and "reward_money" in content:
                print("  ✅ 包含金钱奖励显示")
            else:
                print("  ❌ 缺少金钱奖励显示")
            
            # 检查是否显示声望奖励
            if "声望" in content and "reward_reputation" in content:
                print("  ✅ 包含声望奖励显示")
            else:
                print("  ❌ 缺少声望奖励显示")
            
            # 检查奖励显示格式
            if "badge bg-success" in content and "badge bg-info" in content:
                print("  ✅ 奖励显示格式正确（绿色金钱+蓝色声望）")
                return True
            else:
                print("  ❌ 奖励显示格式不正确")
                return False
                
        else:
            print(f"  ❌ 成就管理页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试成就奖励显示时出错: {e}")
        return False

def test_achievement_data_structure():
    """测试成就数据结构"""
    print("\n📊 测试成就数据结构")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/get_achievements_list", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                achievements = data.get('achievements', {})
                print("  ✅ 成就数据API访问正常")
                
                # 检查各类成就的奖励设置
                reward_check = {}
                total_achievements = 0
                
                for category, category_achievements in achievements.items():
                    if category_achievements:
                        sample_achievement = category_achievements[0]
                        reward_money = sample_achievement.get('reward_money', 0)
                        reward_reputation = sample_achievement.get('reward_reputation', 0)
                        
                        reward_check[category] = {
                            'money': reward_money,
                            'reputation': reward_reputation,
                            'count': len(category_achievements)
                        }
                        total_achievements += len(category_achievements)
                        
                        print(f"  📋 {category}: {len(category_achievements)}个成就")
                        print(f"    💰 金钱奖励: ¥{reward_money:,}")
                        print(f"    🏅 声望奖励: +{reward_reputation}")
                
                print(f"  📊 总成就数: {total_achievements}")
                
                # 验证奖励设置是否合理
                expected_rewards = {
                    '财务成就': {'money': 50000, 'reputation': 200},
                    '员工成就': {'money': 20000, 'reputation': 150},
                    '发展成就': {'money': 100000, 'reputation': 300},
                    '经营成就': {'money': 30000, 'reputation': 250},
                    '特殊成就': {'money': 200000, 'reputation': 500}
                }
                
                correct_rewards = 0
                for category, expected in expected_rewards.items():
                    if category in reward_check:
                        actual = reward_check[category]
                        if (actual['money'] == expected['money'] and 
                            actual['reputation'] == expected['reputation']):
                            correct_rewards += 1
                            print(f"    ✅ {category}奖励设置正确")
                        else:
                            print(f"    ❌ {category}奖励设置不正确")
                
                if correct_rewards >= 3:
                    print("  ✅ 成就奖励数据结构正确")
                    return True
                else:
                    print("  ❌ 成就奖励数据结构不正确")
                    return False
            else:
                print("  ❌ 成就数据API返回失败")
                return False
        else:
            print(f"  ❌ 成就数据API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试成就数据结构时出错: {e}")
        return False

def test_achievement_claim_functionality():
    """测试成就领取功能"""
    print("\n🎁 测试成就领取功能")
    print("-" * 40)
    
    try:
        # 获取当前酒店状态
        response = requests.get(f"{BASE_URL}/api/hotel_info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                current_money = data.get('money', 0)
                current_reputation = data.get('reputation', 0)
                
                print(f"  📊 当前酒店状态:")
                print(f"    💰 资金: ¥{current_money:,}")
                print(f"    🏅 声望: {current_reputation}")
                
                # 检查是否有可领取的成就
                achievements_response = requests.get(f"{BASE_URL}/achievements/get_achievements_list", timeout=10)
                if achievements_response.status_code == 200:
                    achievements_data = achievements_response.json()
                    if achievements_data.get('success'):
                        achievements = achievements_data.get('achievements', {})
                        
                        # 查找已达成但未领取的成就
                        claimable_achievements = []
                        for category, category_achievements in achievements.items():
                            for achievement in category_achievements:
                                if (achievement.get('achieved', False) and 
                                    not achievement.get('reward_claimed', False)):
                                    claimable_achievements.append(achievement)
                        
                        print(f"  🎯 可领取成就数: {len(claimable_achievements)}")
                        
                        if claimable_achievements:
                            print("  ✅ 有可领取的成就，成就领取功能可用")
                            
                            # 显示可领取成就的奖励
                            for achievement in claimable_achievements[:3]:  # 显示前3个
                                money_reward = achievement.get('reward_money', 0)
                                reputation_reward = achievement.get('reward_reputation', 0)
                                print(f"    🏆 {achievement.get('name', '未知成就')}: ¥{money_reward:,} + {reputation_reward}声望")
                            
                            return True
                        else:
                            print("  ⚠️ 暂无可领取成就，但功能结构正常")
                            return True
                    else:
                        print("  ❌ 获取成就列表失败")
                        return False
                else:
                    print("  ❌ 成就列表请求失败")
                    return False
            else:
                print("  ❌ 获取酒店状态失败")
                return False
        else:
            print("  ❌ 酒店状态请求失败")
            return False
    except Exception as e:
        print(f"  ❌ 测试成就领取功能时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 成就奖励修复验证测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项测试
    test_results = []
    test_results.append(("成就奖励显示", test_achievement_rewards_display()))
    test_results.append(("成就数据结构", test_achievement_data_structure()))
    test_results.append(("成就领取功能", test_achievement_claim_functionality()))
    
    print("\n" + "=" * 60)
    print("📋 成就奖励修复验证总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！成就奖励修复完成")
        print("✅ 成就奖励: 同时包含金钱和声望奖励")
        print("✅ 数据结构: reward_money字段已添加并正确设置")
        print("✅ 显示格式: 绿色金钱徽章+蓝色声望徽章")
        print("✅ 领取功能: API路径和字段名已修复")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
    
    print("\n🎯 修复内容:")
    print("💡 数据库结构: 添加reward_money字段")
    print("💡 奖励设置: 根据成就类型设置不同的金钱和声望奖励")
    print("💡 显示界面: 同时显示金钱和声望奖励")
    print("💡 API修复: 修复字段名和路径问题")
    
    print("\n💰 奖励标准:")
    print("  - 财务成就: ¥50,000 + 200声望")
    print("  - 员工成就: ¥20,000 + 150声望")
    print("  - 发展成就: ¥100,000 + 300声望")
    print("  - 经营成就: ¥30,000 + 250声望")
    print("  - 特殊成就: ¥200,000 + 500声望")
    
    print("\n🚀 可以通过浏览器访问查看效果:")
    print("   - 成就管理: http://127.0.0.1:5000/achievements/management")

if __name__ == "__main__":
    main()
