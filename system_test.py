import requests
import json

def test_system():
    base_url = "http://127.0.0.1:5000"
    
    print("测试酒店管理系统功能...")
    
    # 测试首页API
    print("\n1. 测试酒店信息API:")
    try:
        response = requests.get(f"{base_url}/api/hotel_info")
        data = response.json()
        print(f"   状态: {'成功' if data.get('success') else '失败'}")
        print(f"   酒店等级: {data.get('level', 'N/A')}")
        print(f"   当前资金: {data.get('money', 'N/A')}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试入住率API
    print("\n2. 测试入住率API:")
    try:
        response = requests.get(f"{base_url}/api/occupancy_rates")
        data = response.json()
        print(f"   状态: {'成功' if data.get('success') else '失败'}")
        print(f"   房间类型数量: {len(data.get('room_types', []))}")
        print(f"   房间类型: {data.get('room_types', [])}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试繁忙度API
    print("\n3. 测试部门繁忙度API:")
    try:
        response = requests.get(f"{base_url}/api/department_busy_level")
        data = response.json()
        print(f"   状态: {'成功' if data.get('success') else '失败'}")
        if data.get('success'):
            busy_levels = data.get('busy_levels', {})
            for dept, level in busy_levels.items():
                print(f"   {dept}: {level}%")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试部门管理页面
    print("\n4. 测试部门管理页面:")
    try:
        response = requests.get(f"{base_url}/departments/management")
        if response.status_code == 200:
            content = response.text
            print("   页面访问成功")
            # 检查是否显示正确的解锁状态
            if "已解锁" in content and "未解锁" in content:
                print("   页面包含解锁状态信息")
            else:
                print("   页面可能缺少解锁状态信息")
        else:
            print(f"   页面访问失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"   错误: {e}")

if __name__ == "__main__":
    test_system()