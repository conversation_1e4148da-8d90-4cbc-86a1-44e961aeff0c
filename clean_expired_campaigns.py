#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
清理过期的营销活动
"""

from app import create_app, db
from app.models import Hotel, MarketingCampaign

def clean_expired_campaigns():
    """清理过期的营销活动"""
    app = create_app()
    
    with app.app_context():
        hotel = Hotel.query.first()
        if not hotel:
            print("❌ 酒店数据未初始化")
            return
        
        # 查找所有过期的营销活动
        expired_campaigns = MarketingCampaign.query.filter(
            MarketingCampaign.hotel_id == hotel.id,
            MarketingCampaign.ends_at < hotel.date
        ).all()
        
        print(f"📊 找到 {len(expired_campaigns)} 个过期的营销活动")
        
        for campaign in expired_campaigns:
            print(f"  🗑️ 清理过期活动: {campaign.name} (结束日期: {campaign.ends_at})")
            campaign.is_active = False
        
        # 删除所有过期活动记录
        deleted_count = MarketingCampaign.query.filter(
            MarketingCampaign.hotel_id == hotel.id,
            MarketingCampaign.ends_at < hotel.date
        ).delete()
        
        try:
            db.session.commit()
            print(f"✅ 成功清理 {deleted_count} 个过期营销活动")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    clean_expired_campaigns()
