#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试系统实现状态
"""

import requests
import json

def test_system_implementation():
    """测试系统实现状态"""
    print('🔍 全面检查系统实现状态...')
    
    # 1. 检查所有页面访问
    print('\n📄 页面访问检查:')
    pages = [
        ('首页', '/'),
        ('部门管理', '/departments/management'),
        ('员工管理', '/employees/management'),
        ('房间管理', '/rooms/management'),
        ('财务管理', '/finance/management'),
        ('酒店升级', '/hotel/management'),
        ('营销管理', '/marketing/management'),
        ('成就系统', '/achievements/management')
    ]
    
    page_results = {}
    for name, url in pages:
        try:
            r = requests.get(f'http://127.0.0.1:5000{url}')
            page_results[name] = r.status_code == 200
            status = '正常' if r.status_code == 200 else f'HTTP {r.status_code}'
            print(f'  {"✅" if r.status_code == 200 else "❌"} {name}: {status}')
        except Exception as e:
            page_results[name] = False
            print(f'  ❌ {name}: 异常')
    
    # 2. 检查API接口
    print('\n🔌 API接口检查:')
    apis = [
        ('酒店信息', '/api/hotel_info'),
        ('入住率数据', '/api/occupancy_data'),
        ('候选人列表', '/employees/get_candidates_list'),
        ('部门列表', '/departments/get_departments_list'),
        ('房间列表', '/rooms/get_rooms_list'),
        ('财务数据', '/finance/get_financial_data'),
        ('成就列表', '/achievements/get_achievements_list'),
        ('营销活动', '/marketing/get_campaigns_list')
    ]
    
    api_results = {}
    for name, url in apis:
        try:
            r = requests.get(f'http://127.0.0.1:5000{url}')
            api_results[name] = r.status_code == 200
            status = '正常' if r.status_code == 200 else f'HTTP {r.status_code}'
            print(f'  {"✅" if r.status_code == 200 else "❌"} {name}: {status}')
        except Exception as e:
            api_results[name] = False
            print(f'  ❌ {name}: 异常')
    
    # 3. 检查核心功能
    print('\n⚙️ 核心功能检查:')
    functions = [
        ('招聘员工', '/employees/hire', {'candidate_id': 'candidate_0'}),
        ('解锁部门', '/departments/unlock', {'department_id': 'marketing'}),
        ('建设房间', '/rooms/build', {'room_type': '大床房', 'quantity': 1}),
        ('发起营销', '/marketing/start_campaign', {'campaign_type': '网络广告'}),
        ('领取成就', '/achievements/claim', {'achievement_id': 'first_profit'})
    ]
    
    func_results = {}
    for name, url, data in functions:
        try:
            r = requests.post(f'http://127.0.0.1:5000{url}', 
                             headers={'Content-Type': 'application/json'},
                             data=json.dumps(data))
            func_results[name] = r.status_code in [200, 400]  # 400可能是业务逻辑错误，但功能存在
            if r.status_code == 200:
                status = '正常'
            elif r.status_code == 400:
                status = '业务错误(功能存在)'
            else:
                status = f'HTTP {r.status_code}'
            print(f'  {"✅" if func_results[name] else "❌"} {name}: {status}')
        except Exception as e:
            func_results[name] = False
            print(f'  ❌ {name}: 异常')
    
    # 4. 统计结果
    page_success = sum(page_results.values())
    api_success = sum(api_results.values())
    func_success = sum(func_results.values())
    
    print(f'\n📊 实现状态统计:')
    print(f'  📄 页面实现: {page_success}/{len(pages)} ({page_success/len(pages)*100:.0f}%)')
    print(f'  🔌 API实现: {api_success}/{len(apis)} ({api_success/len(apis)*100:.0f}%)')
    print(f'  ⚙️ 功能实现: {func_success}/{len(functions)} ({func_success/len(functions)*100:.0f}%)')
    
    overall_score = (page_success/len(pages) + api_success/len(apis) + func_success/len(functions)) / 3 * 100
    print(f'\n🏆 总体实现度: {overall_score:.1f}%')
    
    # 5. 详细检查缺失功能
    print('\n🔍 缺失功能分析:')
    
    missing_pages = [name for name, ok in page_results.items() if not ok]
    missing_apis = [name for name, ok in api_results.items() if not ok]
    missing_funcs = [name for name, ok in func_results.items() if not ok]
    
    if missing_pages:
        print(f'  ❌ 缺失页面: {", ".join(missing_pages)}')
    if missing_apis:
        print(f'  ❌ 缺失API: {", ".join(missing_apis)}')
    if missing_funcs:
        print(f'  ❌ 缺失功能: {", ".join(missing_funcs)}')
    
    if overall_score >= 90:
        print('\n🎉 系统实现度优秀！')
    elif overall_score >= 70:
        print('\n✅ 系统实现度良好，需要完善部分功能')
    else:
        print('\n⚠️ 系统实现度不足，需要大量开发工作')
    
    return overall_score, missing_pages, missing_apis, missing_funcs

if __name__ == '__main__':
    test_system_implementation()
