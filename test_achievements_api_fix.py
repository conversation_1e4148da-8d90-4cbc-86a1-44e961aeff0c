#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试成就API数据完整性修复
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_achievements_api_data_completeness():
    """测试成就API数据完整性"""
    print("🔍 测试成就API数据完整性")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/get_achievements_list", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                achievements = data.get('achievements', {})
                statistics = data.get('statistics', {})
                
                print(f"  📊 API响应成功")
                print(f"  📈 成就统计: 总数{statistics.get('total', 0)}, 已完成{statistics.get('achieved', 0)}, 完成率{statistics.get('rate', 0)}%")
                
                # 必需字段列表
                required_fields = [
                    'id', 'name', 'description', 'category',
                    'condition_type', 'condition_value', 'achieved',
                    'reward_reputation', 'reward_claimed'
                ]
                
                # 兼容字段列表
                compatibility_fields = [
                    'is_achieved', 'achieved_at', 'achieved_date'
                ]
                
                total_achievements = 0
                complete_achievements = 0
                field_issues = []
                
                print(f"  🔍 检查成就数据完整性:")
                
                for category, category_achievements in achievements.items():
                    print(f"    📂 {category}: {len(category_achievements)}个成就")
                    
                    for achievement in category_achievements:
                        total_achievements += 1
                        achievement_name = achievement.get('name', '未知成就')
                        
                        # 检查必需字段
                        missing_required = []
                        for field in required_fields:
                            if field not in achievement:
                                missing_required.append(field)
                        
                        # 检查兼容字段
                        missing_compatibility = []
                        for field in compatibility_fields:
                            if field not in achievement:
                                missing_compatibility.append(field)
                        
                        if not missing_required:
                            complete_achievements += 1
                            print(f"      ✅ {achievement_name}: 必需字段完整")
                            
                            # 验证字段值的合理性
                            condition_type = achievement.get('condition_type')
                            condition_value = achievement.get('condition_value')
                            
                            if condition_type and condition_value is not None:
                                print(f"        🎯 达成条件: {condition_type} = {condition_value}")
                            else:
                                print(f"        ⚠️ 达成条件值可能为空")
                                
                            reward = achievement.get('reward_reputation', 0)
                            print(f"        🎁 奖励: +{reward}声望")
                            
                        else:
                            print(f"      ❌ {achievement_name}: 缺少必需字段 {missing_required}")
                            field_issues.append(f"{achievement_name}: {missing_required}")
                        
                        if missing_compatibility:
                            print(f"        ⚠️ 缺少兼容字段: {missing_compatibility}")
                
                # 总结
                completeness_rate = (complete_achievements / total_achievements * 100) if total_achievements > 0 else 0
                print(f"  📊 数据完整性统计:")
                print(f"    - 总成就数: {total_achievements}")
                print(f"    - 完整成就数: {complete_achievements}")
                print(f"    - 完整性率: {completeness_rate:.1f}%")
                
                if completeness_rate == 100:
                    print(f"  ✅ 所有成就数据完整，无缺失字段")
                else:
                    print(f"  ❌ 存在数据不完整的成就:")
                    for issue in field_issues[:5]:  # 只显示前5个问题
                        print(f"    - {issue}")
                
                return completeness_rate == 100
                
            else:
                print(f"  ❌ API返回失败: {data.get('message', '')}")
                return False
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试成就API数据完整性时出错: {e}")
        return False

def test_achievements_page_display():
    """测试成就页面显示"""
    print("\n📄 测试成就页面显示")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/management", timeout=10)
        if response.status_code == 200:
            content = response.text
            print("  ✅ 成就管理页面访问正常")
            
            # 检查表格显示
            if "达成要求" in content:
                print("  ✅ 包含达成要求列")
            else:
                print("  ❌ 缺少达成要求列")
            
            # 检查具体的达成要求显示
            requirement_patterns = [
                "资金达到", "酒店达到", "员工数达到", "运营", "满意度达到", "声望达到"
            ]
            
            found_patterns = 0
            for pattern in requirement_patterns:
                if pattern in content:
                    found_patterns += 1
                    print(f"    ✅ 包含'{pattern}'类型要求")
                else:
                    print(f"    ❌ 缺少'{pattern}'类型要求")
            
            print(f"  📊 达成要求类型: {found_patterns}/{len(requirement_patterns)}")
            
            # 检查奖励显示
            if "达成奖励" in content and "声望" in content:
                print("  ✅ 包含达成奖励显示")
            else:
                print("  ❌ 缺少达成奖励显示")
            
            return found_patterns >= 4  # 至少4种类型的要求
            
        else:
            print(f"  ❌ 成就管理页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试成就页面显示时出错: {e}")
        return False

def test_specific_achievement_data():
    """测试特定成就的数据"""
    print("\n🎯 测试特定成就的数据")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/get_achievements_list", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                achievements = data.get('achievements', {})
                
                # 测试几个具体的成就
                test_achievements = [
                    ('财务成就', '初次盈利'),
                    ('财务成就', '百万富翁'),
                    ('员工成就', '首位员工'),
                    ('发展成就', '星光初现')
                ]
                
                for category, achievement_name in test_achievements:
                    category_achievements = achievements.get(category, [])
                    target_achievement = None
                    
                    for ach in category_achievements:
                        if ach.get('name') == achievement_name:
                            target_achievement = ach
                            break
                    
                    if target_achievement:
                        print(f"  🎯 {achievement_name}:")
                        print(f"    - ID: {target_achievement.get('id')}")
                        print(f"    - 描述: {target_achievement.get('description', '无')}")
                        print(f"    - 条件类型: {target_achievement.get('condition_type', '无')}")
                        print(f"    - 条件值: {target_achievement.get('condition_value', '无')}")
                        print(f"    - 已达成: {target_achievement.get('achieved', False)}")
                        print(f"    - 奖励声望: {target_achievement.get('reward_reputation', 0)}")
                        
                        # 验证数据合理性
                        if target_achievement.get('condition_type') and target_achievement.get('condition_value') is not None:
                            print(f"    ✅ 数据完整且合理")
                        else:
                            print(f"    ❌ 数据不完整或不合理")
                    else:
                        print(f"  ❌ 未找到成就: {achievement_name}")
                
                return True
            else:
                print(f"  ❌ API返回失败: {data.get('message', '')}")
                return False
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试特定成就数据时出错: {e}")
        return False

def test_field_compatibility():
    """测试字段兼容性"""
    print("\n🔄 测试字段兼容性")
    print("-" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/achievements/get_achievements_list", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                achievements = data.get('achievements', {})
                
                # 检查第一个成就的字段兼容性
                first_achievement = None
                for category_achievements in achievements.values():
                    if category_achievements:
                        first_achievement = category_achievements[0]
                        break
                
                if first_achievement:
                    print(f"  🔍 检查成就字段兼容性: {first_achievement.get('name', '未知')}")
                    
                    # 检查新旧字段名兼容性
                    compatibility_checks = [
                        ('achieved', 'is_achieved'),
                        ('achieved_date', 'achieved_at')
                    ]
                    
                    for new_field, old_field in compatibility_checks:
                        new_value = first_achievement.get(new_field)
                        old_value = first_achievement.get(old_field)
                        
                        if new_value == old_value:
                            print(f"    ✅ {new_field} = {old_field}: 兼容性正常")
                        else:
                            print(f"    ❌ {new_field} ≠ {old_field}: 兼容性问题")
                    
                    # 检查所有字段
                    all_fields = list(first_achievement.keys())
                    print(f"    📋 所有字段: {', '.join(all_fields)}")
                    
                    expected_count = 14  # 预期字段数量
                    if len(all_fields) >= expected_count:
                        print(f"    ✅ 字段数量充足: {len(all_fields)}>={expected_count}")
                    else:
                        print(f"    ❌ 字段数量不足: {len(all_fields)}<{expected_count}")
                    
                    return len(all_fields) >= expected_count
                else:
                    print(f"  ❌ 未找到任何成就数据")
                    return False
            else:
                print(f"  ❌ API返回失败: {data.get('message', '')}")
                return False
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 测试字段兼容性时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 成就API数据完整性修复验证")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 执行各项测试
    test_results = []
    test_results.append(("API数据完整性", test_achievements_api_data_completeness()))
    test_results.append(("页面显示正常", test_achievements_page_display()))
    test_results.append(("特定成就数据", test_specific_achievement_data()))
    test_results.append(("字段兼容性", test_field_compatibility()))
    
    print("\n" + "=" * 60)
    print("📋 成就API修复验证总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！成就API数据完整性已修复")
        print("✅ 必需字段完整: condition_type, condition_value, achieved等")
        print("✅ 兼容字段支持: is_achieved, achieved_at等")
        print("✅ 页面显示正常: 达成要求和奖励正确显示")
        print("✅ 数据合理性: 所有字段值都符合预期")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
    
    print("\n🚀 可以通过浏览器访问查看效果:")
    print("   - 成就管理: http://127.0.0.1:5000/achievements/management")
    print("   - 成就API: http://127.0.0.1:5000/achievements/get_achievements_list")

if __name__ == "__main__":
    main()
