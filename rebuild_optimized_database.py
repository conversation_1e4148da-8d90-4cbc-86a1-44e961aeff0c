#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重建优化的数据库 - 解决锁定问题的根本方案
"""

from app import create_app, db
from app.models import Hotel, Room, Employee, Department, FinancialRecord, Achievement
from sqlalchemy import text
from datetime import datetime

def rebuild_database():
    """重建数据库并应用优化"""
    app = create_app()
    with app.app_context():
        print('🔄 重建数据库...')
        
        # 1. 删除所有表
        try:
            db.drop_all()
            print('✅ 删除旧表完成')
        except Exception as e:
            print(f'⚠️ 删除旧表警告: {e}')
        
        # 2. 创建所有表
        try:
            db.create_all()
            print('✅ 创建新表完成')
        except Exception as e:
            print(f'❌ 创建表失败: {e}')
            return False
        
        # 3. 立即应用SQLite优化配置
        print('⚡ 应用SQLite优化配置...')
        optimizations = [
            "PRAGMA journal_mode=WAL",           # WAL模式，支持并发读写
            "PRAGMA synchronous=NORMAL",         # 正常同步模式
            "PRAGMA cache_size=10000",           # 增大缓存
            "PRAGMA temp_store=memory",          # 临时表存储在内存
            "PRAGMA mmap_size=268435456",        # 256MB内存映射
            "PRAGMA busy_timeout=30000",         # 30秒忙等超时
            "PRAGMA wal_autocheckpoint=1000"     # WAL自动检查点
        ]
        
        for sql in optimizations:
            try:
                db.session.execute(text(sql))
                print(f'✅ {sql.split("=")[0]}')
            except Exception as e:
                print(f'❌ {sql} - 错误: {e}')
        
        # 4. 添加关键索引
        print('📊 添加性能索引...')
        indexes = [
            # 最关键的索引 - 外键索引
            "CREATE INDEX idx_room_hotel_id ON room(hotel_id)",
            "CREATE INDEX idx_employee_hotel_id ON employee(hotel_id)",
            "CREATE INDEX idx_department_hotel_id ON department(hotel_id)",
            "CREATE INDEX idx_financial_hotel_id ON financial_record(hotel_id)",
            "CREATE INDEX idx_achievement_hotel_id ON achievement(hotel_id)",
            
            # 查询优化索引
            "CREATE INDEX idx_financial_date ON financial_record(record_date)",
            "CREATE INDEX idx_financial_hotel_date ON financial_record(hotel_id, record_date DESC)",
            "CREATE INDEX idx_employee_dept ON employee(hotel_id, department)",
            "CREATE INDEX idx_room_type ON room(hotel_id, type)",
            "CREATE INDEX idx_achievement_status ON achievement(hotel_id, achieved)"
        ]
        
        for sql in indexes:
            try:
                db.session.execute(text(sql))
                index_name = sql.split()[-1].split('(')[0]
                print(f'✅ {index_name}')
            except Exception as e:
                print(f'❌ 索引创建失败: {e}')
        
        # 5. 初始化基础数据
        print('🏨 初始化基础数据...')
        
        # 创建酒店
        hotel = Hotel(
            name='红珊瑚大酒店',
            level=1,
            money=100000,
            satisfaction=60.0,
            reputation=1000,
            reputation_level='新兴酒店',
            date=datetime(1990, 1, 1),
            days_elapsed=0,
            time_running=True,
            time_speed=1
        )
        db.session.add(hotel)
        
        # 提交酒店数据
        try:
            db.session.commit()
            print('✅ 酒店数据创建完成')
        except Exception as e:
            print(f'❌ 酒店数据创建失败: {e}')
            db.session.rollback()
            return False
        
        # 创建基础房间
        basic_rooms = [
            {'type': '标准间', 'count': 10, 'price': 200},
            {'type': '豪华间', 'count': 5, 'price': 400},
            {'type': '商务套房', 'count': 2, 'price': 800},
            {'type': '总统套房', 'count': 1, 'price': 1500}
        ]
        
        for room_data in basic_rooms:
            room = Room(
                hotel_id=hotel.id,
                type=room_data['type'],
                count=room_data['count'],
                price=room_data['price']
            )
            db.session.add(room)
        
        # 创建基础部门
        departments = [
            {'name': '前台', 'is_unlocked': True},
            {'name': '客房部', 'is_unlocked': True},
            {'name': '餐饮部', 'is_unlocked': False},
            {'name': '财务部', 'is_unlocked': False},
            {'name': '工程部', 'is_unlocked': False},
            {'name': '安保部', 'is_unlocked': False},
            {'name': '康养部', 'is_unlocked': False},
            {'name': '董事会', 'is_unlocked': False}
        ]
        
        for dept_data in departments:
            dept = Department(
                hotel_id=hotel.id,
                name=dept_data['name'],
                is_unlocked=dept_data['is_unlocked']
            )
            db.session.add(dept)
        
        # 初始化成就系统
        achievements_data = [
            {'name': '初次盈利', 'description': '酒店资金达到10万元', 'category': '财务成就'},
            {'name': '百万富翁', 'description': '酒店资金达到100万元', 'category': '财务成就'},
            {'name': '首位员工', 'description': '招聘第一名员工', 'category': '员工成就'},
            {'name': '星光初现', 'description': '酒店达到2星', 'category': '发展成就'},
            {'name': '满意服务', 'description': '客户满意度达到90分以上', 'category': '经营成就'}
        ]
        
        for achievement_data in achievements_data:
            achievement = Achievement(
                hotel_id=hotel.id,
                name=achievement_data['name'],
                description=achievement_data['description'],
                category=achievement_data['category'],
                condition_type='custom',
                condition_value=0,
                reward_reputation=100,
                achieved=False,
                reward_claimed=False
            )
            db.session.add(achievement)
        
        # 提交所有数据
        try:
            db.session.commit()
            print('✅ 基础数据初始化完成')
        except Exception as e:
            print(f'❌ 基础数据初始化失败: {e}')
            db.session.rollback()
            return False
        
        # 6. 验证优化结果
        print('📊 验证数据库状态...')
        
        # 检查配置
        result = db.session.execute(text('PRAGMA journal_mode'))
        journal_mode = result.fetchone()[0]
        
        result = db.session.execute(text('PRAGMA synchronous'))
        synchronous = result.fetchone()[0]
        
        result = db.session.execute(text('PRAGMA busy_timeout'))
        busy_timeout = result.fetchone()[0]
        
        # 检查索引
        result = db.session.execute(text('SELECT COUNT(*) FROM sqlite_master WHERE type="index" AND name NOT LIKE "sqlite_%"'))
        index_count = result.fetchone()[0]
        
        # 检查数据
        hotel_count = Hotel.query.count()
        room_count = Room.query.count()
        dept_count = Department.query.count()
        achievement_count = Achievement.query.count()
        
        print(f'✅ Journal模式: {journal_mode}')
        print(f'✅ 同步模式: {synchronous}')
        print(f'✅ 忙等超时: {busy_timeout}ms')
        print(f'✅ 索引数量: {index_count}')
        print(f'✅ 数据统计: 酒店{hotel_count}, 房间{room_count}, 部门{dept_count}, 成就{achievement_count}')
        
        return True

if __name__ == '__main__':
    print('🚀 开始重建优化数据库...')
    if rebuild_database():
        print('\n🎉 数据库重建完成！')
        print('💡 优化效果:')
        print('   - WAL模式支持并发读写')
        print('   - 添加了10个关键索引')
        print('   - 30秒忙等超时')
        print('   - 256MB内存映射')
        print('   - 基础数据已初始化')
        print('\n🔥 数据库锁定问题应该彻底解决！')
    else:
        print('\n❌ 数据库重建失败！')
