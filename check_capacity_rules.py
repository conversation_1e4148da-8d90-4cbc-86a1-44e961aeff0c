import json
from app import create_app
from app.models import GameSetting

app = create_app()
with app.app_context():
    setting = GameSetting.query.filter_by(key='employee_capacity_rules').first()
    print('Setting exists:', setting is not None)
    if setting:
        print('Value:', setting.value)
        try:
            rules = json.loads(setting.value)
            print('Parsed rules:')
            for dept, dept_rules in rules.items():
                print(f'  {dept}: {dept_rules}')
        except json.JSONDecodeError as e:
            print('JSON decode error:', e)
    else:
        print('No employee_capacity_rules setting found in database')