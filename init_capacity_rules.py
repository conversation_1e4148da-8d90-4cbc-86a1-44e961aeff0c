import json
from app import create_app
from app.models import GameSetting

# 员工服务能力规则 (基于需求文档3.5.2节)
employee_capacity_rules = {
    "前台部": {
        "初级": 20,
        "中级": 50,
        "高级": 100,
        "特级": 200
    },
    "客房部": {
        "初级": 15,
        "中级": 30,
        "高级": 60,
        "特级": 100
    },
    "餐饮部": {
        "初级": 20,
        "中级": 50,
        "高级": 100,
        "特级": 200
    },
    "安保部": {
        "初级": 20,
        "中级": 50,
        "高级": 100,
        "特级": 200
    },
    "财务部": {
        "初级": 30,
        "中级": 50,
        "高级": 80,
        "特级": 150
    },
    "商务部": {
        "初级": 50,
        "中级": 100,
        "高级": 200,
        "特级": 300
    }
}

app = create_app()
with app.app_context():
    # 检查是否已存在
    setting = GameSetting.query.filter_by(key='employee_capacity_rules').first()
    if setting:
        # 更新现有设置
        setting.value = json.dumps(employee_capacity_rules, ensure_ascii=False)
        print("更新设置: employee_capacity_rules")
    else:
        # 创建新设置
        setting = GameSetting(key='employee_capacity_rules', value=json.dumps(employee_capacity_rules, ensure_ascii=False))
        from app import db
        db.session.add(setting)
        print("创建设置: employee_capacity_rules")
    
    # 提交更改
    try:
        from app import db
        db.session.commit()
        print("员工服务能力规则初始化完成")
    except Exception as e:
        print(f"保存员工服务能力规则时出错: {e}")
        from app import db
        db.session.rollback()