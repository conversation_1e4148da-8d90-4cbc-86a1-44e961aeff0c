#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Flask路由注册
"""

from app import create_app

def test_routes():
    """测试路由注册"""
    app = create_app()
    
    print("🔍 检查Flask路由注册")
    print("=" * 40)
    
    # 获取所有路由
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append((rule.rule, rule.methods, rule.endpoint))
    
    # 查找员工相关路由
    employee_routes = [r for r in routes if '/employees' in r[0]]
    
    print(f"📊 员工相关路由 ({len(employee_routes)} 个):")
    for route, methods, endpoint in employee_routes:
        print(f"  {route} [{', '.join(methods)}] -> {endpoint}")
    
    # 检查特定路由
    target_route = "/employees/hire_by_department"
    found = any(route == target_route for route, _, _ in employee_routes)
    
    if found:
        print(f"\n✅ 找到目标路由: {target_route}")
    else:
        print(f"\n❌ 未找到目标路由: {target_route}")
        print(f"💡 可能的原因:")
        print(f"  - 语法错误导致函数未注册")
        print(f"  - 缩进问题")
        print(f"  - 导入错误")

if __name__ == "__main__":
    test_routes()
