#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

print('🧪 开始测试API功能...')

# 测试获取酒店信息
print('\n1. 测试获取酒店信息:')
try:
    r = requests.get('http://127.0.0.1:5000/api/hotel_info')
    if r.status_code == 200:
        data = r.json()
        print(f'✅ 酒店名称: {data["hotel_name"]}')
        print(f'✅ 酒店等级: {data["level"]}星')
        print(f'✅ 当前资金: ¥{data["money"]:,}')
        print(f'✅ 满意度: {data["satisfaction"]:.1f}分')
        print(f'✅ 时间状态: {"运行中" if data["time_running"] else "已暂停"}')
        print(f'✅ 时间速度: {data["time_speed"]}倍速')
        print(f'✅ 房间总数: {data["total_rooms"]}')
        print(f'✅ 客户总数: {data["total_guests"]:.0f}')
        print(f'✅ 员工总数: {data["employee_count"]}')
    else:
        print(f'❌ 获取酒店信息失败: {r.status_code}')
except Exception as e:
    print(f'❌ 请求失败: {e}')

# 测试暂停时间
print('\n2. 测试暂停时间:')
try:
    r = requests.post('http://127.0.0.1:5000/api/toggle_time', 
                     headers={'Content-Type': 'application/json'})
    if r.status_code == 200:
        data = r.json()
        print(f'✅ {data["message"]}')
    else:
        print(f'❌ 暂停时间失败: {r.status_code}')
except Exception as e:
    print(f'❌ 请求失败: {e}')

# 测试切换速度
print('\n3. 测试切换速度:')
try:
    r = requests.post('http://127.0.0.1:5000/api/toggle_time_speed', 
                     headers={'Content-Type': 'application/json'})
    if r.status_code == 200:
        data = r.json()
        print(f'✅ {data["message"]}')
    else:
        print(f'❌ 切换速度失败: {r.status_code}')
except Exception as e:
    print(f'❌ 请求失败: {e}')

# 测试推进时间
print('\n4. 测试推进时间:')
try:
    r = requests.post('http://127.0.0.1:5000/api/advance_time', 
                     headers={'Content-Type': 'application/json'})
    if r.status_code == 200:
        data = r.json()
        print(f'✅ {data["message"]}')
    else:
        print(f'❌ 推进时间失败: {r.status_code}')
except Exception as e:
    print(f'❌ 请求失败: {e}')

# 测试继续时间
print('\n5. 测试继续时间:')
try:
    r = requests.post('http://127.0.0.1:5000/api/toggle_time', 
                     headers={'Content-Type': 'application/json'})
    if r.status_code == 200:
        data = r.json()
        print(f'✅ {data["message"]}')
    else:
        print(f'❌ 继续时间失败: {r.status_code}')
except Exception as e:
    print(f'❌ 请求失败: {e}')

# 测试入住率数据
print('\n6. 测试入住率数据:')
try:
    r = requests.get('http://127.0.0.1:5000/api/occupancy_data')
    if r.status_code == 200:
        data = r.json()
        room_types = list(data.keys())
        print(f'✅ 房间类型数量: {len(room_types)}')
        if room_types:
            print(f'✅ 房间类型: {", ".join(room_types[:3])}...')
            first_room = data[room_types[0]]
            if first_room:
                print(f'✅ 数据天数: {len(first_room)}天')
                print(f'✅ 最新入住率: {first_room[-1]["rate"]}%')
    else:
        print(f'❌ 获取入住率数据失败: {r.status_code}')
except Exception as e:
    print(f'❌ 请求失败: {e}')

print('\n🎉 API功能测试完成！所有功能正常工作！')
