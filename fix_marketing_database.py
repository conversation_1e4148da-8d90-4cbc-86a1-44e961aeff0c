#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复营销活动数据库问题
"""

from app import create_app, db
from app.models import Hotel, MarketingCampaign

def fix_marketing_database():
    """修复营销活动数据库"""
    app = create_app()
    
    with app.app_context():
        hotel = Hotel.query.first()
        if not hotel:
            print("❌ 酒店数据未初始化")
            return
        
        print(f"🏨 当前酒店时间: {hotel.date}")
        
        # 查找所有营销活动
        all_campaigns = MarketingCampaign.query.filter_by(hotel_id=hotel.id).all()
        print(f"📊 数据库中共有 {len(all_campaigns)} 个营销活动记录")
        
        for campaign in all_campaigns:
            print(f"  📢 {campaign.name}: 开始{campaign.started_at}, 结束{campaign.ends_at}, 活跃{campaign.is_active}")
            
            # 检查是否过期
            if campaign.ends_at < hotel.date:
                print(f"    🗑️ 活动已过期，设置为非活跃")
                campaign.is_active = False
            elif campaign.ends_at == hotel.date:
                print(f"    ⏰ 活动今天结束，设置为非活跃")
                campaign.is_active = False
        
        # 删除所有营销活动记录，重新开始
        print(f"\n🗑️ 清理所有营销活动记录...")
        deleted_count = MarketingCampaign.query.filter_by(hotel_id=hotel.id).delete()
        
        try:
            db.session.commit()
            print(f"✅ 成功删除 {deleted_count} 个营销活动记录")
            print(f"💡 现在所有营销活动都可以重新启动")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    fix_marketing_database()
