#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试酒店管理系统的所有功能
"""

import requests
import json
import time

def test_all_features():
    """测试所有功能"""
    print('🎯 全面测试酒店管理系统...')
    
    base_url = 'http://127.0.0.1:5000'
    
    # 1. 测试页面访问
    print('\n📄 页面访问测试:')
    pages = [
        ('首页', '/'),
        ('部门管理', '/departments/management'),
        ('员工管理', '/employees/management'),
        ('房间管理', '/rooms/management'),
        ('财务管理', '/finance/management'),
        ('酒店升级', '/hotel/management'),
        ('营销管理', '/marketing/management'),
        ('成就系统', '/achievements/management')
    ]
    
    page_results = []
    for name, url in pages:
        try:
            r = requests.get(f'{base_url}{url}', timeout=5)
            success = r.status_code == 200
            page_results.append(success)
            print(f'  {"✅" if success else "❌"} {name}: {"正常" if success else f"HTTP {r.status_code}"}')
        except Exception as e:
            page_results.append(False)
            print(f'  ❌ {name}: 异常 - {str(e)[:50]}')
    
    # 2. 测试核心API
    print('\n🔌 核心API测试:')
    apis = [
        ('酒店信息', '/api/hotel_info'),
        ('入住率数据', '/api/occupancy_data'),
        ('候选人列表', '/employees/get_candidates_list')
    ]
    
    api_results = []
    for name, url in apis:
        try:
            r = requests.get(f'{base_url}{url}', timeout=5)
            success = r.status_code == 200
            api_results.append(success)
            print(f'  {"✅" if success else "❌"} {name}: {"正常" if success else f"HTTP {r.status_code}"}')
            
            if success and name == '酒店信息':
                data = r.json()
                print(f'    酒店等级: {data.get("level", "未知")}星')
                print(f'    资金: ¥{data.get("money", 0):,}')
                print(f'    满意度: {data.get("satisfaction", 0):.1f}分')
                
        except Exception as e:
            api_results.append(False)
            print(f'  ❌ {name}: 异常 - {str(e)[:50]}')
    
    # 3. 测试核心功能
    print('\n⚙️ 核心功能测试:')
    
    # 3.1 测试招聘功能
    try:
        hire_data = {'candidate_id': 'candidate_0'}
        r = requests.post(f'{base_url}/employees/hire', 
                         headers={'Content-Type': 'application/json'},
                         data=json.dumps(hire_data),
                         timeout=5)
        if r.status_code == 200:
            result = r.json()
            print(f'  ✅ 招聘功能: {result.get("message", "成功")}')
        else:
            print(f'  ⚠️ 招聘功能: HTTP {r.status_code} (可能是业务逻辑限制)')
    except Exception as e:
        print(f'  ❌ 招聘功能: 异常 - {str(e)[:50]}')
    
    # 3.2 测试建设房间功能
    try:
        build_data = {'room_type': '标准间', 'quantity': 1}
        r = requests.post(f'{base_url}/rooms/build', 
                         headers={'Content-Type': 'application/json'},
                         data=json.dumps(build_data),
                         timeout=5)
        if r.status_code == 200:
            result = r.json()
            print(f'  ✅ 建设房间: {result.get("message", "成功")}')
        else:
            print(f'  ⚠️ 建设房间: HTTP {r.status_code} (可能是资金不足)')
    except Exception as e:
        print(f'  ❌ 建设房间: 异常 - {str(e)[:50]}')
    
    # 4. 测试时间推进系统
    print('\n⏰ 时间推进系统测试:')
    try:
        # 获取初始时间
        r1 = requests.get(f'{base_url}/api/hotel_info', timeout=5)
        if r1.status_code == 200:
            data1 = r1.json()
            initial_date = data1.get('date')
            initial_money = data1.get('money', 0)
            print(f'  初始状态: {initial_date}, 资金: ¥{initial_money:,}')
            
            # 等待时间推进
            print('  等待时间推进...')
            time.sleep(6)
            
            # 获取更新后的时间
            r2 = requests.get(f'{base_url}/api/hotel_info', timeout=5)
            if r2.status_code == 200:
                data2 = r2.json()
                new_date = data2.get('date')
                new_money = data2.get('money', 0)
                
                if initial_date != new_date:
                    money_change = new_money - initial_money
                    print(f'  ✅ 时间推进: {initial_date} → {new_date}')
                    print(f'  💰 资金变化: {money_change:+,}')
                else:
                    print('  ⚠️ 时间推进: 未检测到变化（可能已暂停）')
            else:
                print('  ❌ 时间推进: 无法获取更新数据')
        else:
            print('  ❌ 时间推进: 无法获取初始数据')
    except Exception as e:
        print(f'  ❌ 时间推进: 异常 - {str(e)[:50]}')
    
    # 5. 测试界面元素
    print('\n🎨 界面元素测试:')
    try:
        r = requests.get(f'{base_url}/', timeout=5)
        if r.status_code == 200:
            content = r.text
            
            ui_elements = {
                '酒店名称': 'hotelName' in content,
                '修改图标': 'bi-pencil-square' in content,
                '数据统计卡片': 'col-xl-3 col-md-6' in content,
                '游戏控制按钮': 'toggleTimeBtn' in content,
                '快捷管理按钮': 'departments.management' in content,
                '入住率图表': 'occupancyChart' in content,
                '统一按钮样式': 'height: 28px' in content,
                '全屏布局': 'height: 100%' in content
            }
            
            for element, present in ui_elements.items():
                print(f'  {"✅" if present else "❌"} {element}: {"存在" if present else "缺失"}')
        else:
            print('  ❌ 无法访问首页进行界面测试')
    except Exception as e:
        print(f'  ❌ 界面测试: 异常 - {str(e)[:50]}')
    
    # 6. 生成测试报告
    print('\n📊 测试报告:')
    page_success_rate = sum(page_results) / len(page_results) * 100
    api_success_rate = sum(api_results) / len(api_results) * 100
    
    print(f'  📄 页面访问成功率: {page_success_rate:.0f}% ({sum(page_results)}/{len(page_results)})')
    print(f'  🔌 API功能成功率: {api_success_rate:.0f}% ({sum(api_results)}/{len(api_results)})')
    
    overall_score = (page_success_rate + api_success_rate) / 2
    print(f'  🏆 总体评分: {overall_score:.1f}%')
    
    if overall_score >= 90:
        print('\n🎉 系统状态优秀！所有核心功能正常工作')
    elif overall_score >= 70:
        print('\n✅ 系统状态良好，大部分功能正常')
    else:
        print('\n⚠️ 系统需要进一步完善')
    
    print('\n🚀 测试完成！')
    return overall_score

if __name__ == '__main__':
    test_all_features()
