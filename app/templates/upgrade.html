{% extends "base.html" %}

{% block title %}酒店升级 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-arrow-up-circle-fill text-primary me-2"></i>酒店升级</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 当前酒店状态 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-star-fill text-warning me-2"></i>当前酒店状态
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-star-fill text-warning fs-3 mb-2"></i>
                            <h4 class="text-warning mb-1">{{ hotel.level }}星</h4>
                            <small class="text-muted">当前等级</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-cash-stack text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">¥{{ "{:,}".format(hotel.money) }}</h4>
                            <small class="text-muted">当前资金</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-trophy-fill text-info fs-3 mb-2"></i>
                            <h4 class="text-info mb-1">{{ hotel.reputation }}</h4>
                            <small class="text-muted">声望值</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-calendar-check text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">{{ hotel.days_elapsed }}天</h4>
                            <small class="text-muted">运营天数</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 升级列表 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-list-stars text-primary me-2"></i>升级列表
                </h5>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">状态</th>
                                <th width="15%">等级</th>
                                <th width="15%">升级费用</th>
                                <th width="20%">可解锁房间</th>
                                <th width="20%">可解锁部门</th>
                                <th width="15%">要求</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set upgrade_levels = [
                                {
                                    'level': 2,
                                    'cost': 1000000,
                                    'reputation_required': 500,
                                    'satisfaction_required': 60,
                                    'days_required': 30,
                                    'rooms': ['大床房'],
                                    'departments': ['营销部'],
                                    'requirements': '声望500+, 满意度60+, 运营30天'
                                },
                                {
                                    'level': 3,
                                    'cost': 3000000,
                                    'reputation_required': 1500,
                                    'satisfaction_required': 65,
                                    'days_required': 90,
                                    'rooms': ['家庭房'],
                                    'departments': ['餐饮部'],
                                    'requirements': '声望1500+, 满意度65+, 运营90天'
                                },
                                {
                                    'level': 4,
                                    'cost': 8000000,
                                    'reputation_required': 3000,
                                    'satisfaction_required': 70,
                                    'days_required': 180,
                                    'rooms': ['商务间'],
                                    'departments': ['安保部'],
                                    'requirements': '声望3000+, 满意度70+, 运营180天'
                                },
                                {
                                    'level': 5,
                                    'cost': 20000000,
                                    'reputation_required': 6000,
                                    'satisfaction_required': 75,
                                    'days_required': 365,
                                    'rooms': ['行政间'],
                                    'departments': ['财务部'],
                                    'requirements': '声望6000+, 满意度75+, 运营365天'
                                },
                                {
                                    'level': 6,
                                    'cost': 50000000,
                                    'reputation_required': 12000,
                                    'satisfaction_required': 80,
                                    'days_required': 730,
                                    'rooms': ['豪华间'],
                                    'departments': ['商务部'],
                                    'requirements': '声望12000+, 满意度80+, 运营730天'
                                },
                                {
                                    'level': 7,
                                    'cost': 100000000,
                                    'reputation_required': 25000,
                                    'satisfaction_required': 85,
                                    'days_required': 1095,
                                    'rooms': ['总统套房'],
                                    'departments': ['工程部'],
                                    'requirements': '声望25000+, 满意度85+, 运营1095天'
                                },
                                {
                                    'level': 8,
                                    'cost': 200000000,
                                    'reputation_required': 50000,
                                    'satisfaction_required': 90,
                                    'days_required': 1460,
                                    'rooms': ['皇家套房'],
                                    'departments': ['康养部'],
                                    'requirements': '声望50000+, 满意度90+, 运营1460天'
                                },
                                {
                                    'level': 9,
                                    'cost': 500000000,
                                    'reputation_required': 100000,
                                    'satisfaction_required': 95,
                                    'days_required': 1825,
                                    'rooms': ['总统别墅', '皇宫套房'],
                                    'departments': ['董事会'],
                                    'requirements': '声望100000+, 满意度95+, 运营1825天'
                                }
                            ] %}

                            {% for upgrade in upgrade_levels %}
                            <tr class="{% if upgrade.level <= hotel.level %}table-success{% elif upgrade.level == hotel.level + 1 %}table-warning{% endif %}">
                                <td>
                                    {% if upgrade.level <= hotel.level %}
                                    <i class="bi bi-check-circle-fill text-success fs-5"></i>
                                    {% elif upgrade.level == hotel.level + 1 %}
                                    <i class="bi bi-arrow-up-circle text-warning fs-5"></i>
                                    {% else %}
                                    <i class="bi bi-lock-fill text-secondary fs-5"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            {% for i in range(upgrade.level) %}
                                            <i class="bi bi-star-fill text-warning"></i>
                                            {% endfor %}
                                            {% for i in range(9 - upgrade.level) %}
                                            <i class="bi bi-star text-muted"></i>
                                            {% endfor %}
                                        </div>
                                        <strong>{{ upgrade.level }}星级</strong>
                                    </div>
                                </td>
                                <td>
                                    {% if upgrade.level <= hotel.level %}
                                    <span class="text-success">已支付</span>
                                    {% elif hotel.money >= upgrade.cost %}
                                    <span class="text-success">¥{{ "{:,}".format(upgrade.cost) }}</span>
                                    {% else %}
                                    <span class="text-danger">¥{{ "{:,}".format(upgrade.cost) }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if upgrade.level > hotel.level %}
                                        {% if upgrade.rooms %}
                                            {% for room in upgrade.rooms %}
                                            <span class="badge bg-info me-1">{{ room }}</span>
                                            {% endfor %}
                                        {% else %}
                                        <span class="text-muted">无新房型</span>
                                        {% endif %}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if upgrade.level > hotel.level %}
                                        {% if upgrade.departments %}
                                            {% for dept in upgrade.departments %}
                                            <span class="badge bg-primary me-1">{{ dept }}</span>
                                            {% endfor %}
                                        {% else %}
                                        <span class="text-muted">无新部门</span>
                                        {% endif %}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ upgrade.requirements }}</small>
                                </td>
                                <td>
                                    {% if upgrade.level <= hotel.level %}
                                    <span class="text-success">已完成</span>
                                    {% elif upgrade.level == hotel.level + 1 %}
                                        {% set money_ok = hotel.money >= upgrade.cost %}
                                        {% set reputation_ok = hotel.reputation >= upgrade.reputation_required %}
                                        {% set satisfaction_ok = hotel.satisfaction >= upgrade.satisfaction_required %}
                                        {% set days_ok = hotel.days_elapsed >= upgrade.days_required %}
                                        {% set can_upgrade = money_ok and reputation_ok and satisfaction_ok and days_ok %}

                                        {% if can_upgrade %}
                                        <button class="btn btn-sm btn-success" onclick="upgradeHotel({{ upgrade.level }})">
                                            <i class="bi bi-arrow-up me-1"></i>升级
                                        </button>
                                        {% else %}
                                        <button class="btn btn-sm btn-secondary" disabled title="资金:{{ '✓' if money_ok else '✗' }} 声望:{{ '✓' if reputation_ok else '✗' }} 满意度:{{ '✓' if satisfaction_ok else '✗' }} 天数:{{ '✓' if days_ok else '✗' }}">
                                            <i class="bi bi-x-circle me-1"></i>条件不足
                                        </button>
                                        {% endif %}
                                    {% else %}
                                    <button class="btn btn-sm btn-secondary" disabled>
                                        <i class="bi bi-lock me-1"></i>等级不足
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function upgradeHotel(level) {
    if (confirm(`确定要升级到${level}星酒店吗？这将花费大量资金！`)) {
        apiRequest('/hotel/upgrade', {
            method: 'POST',
            body: JSON.stringify({level: level})
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('升级失败', 'error');
        });
    }
}
</script>
{% endblock %}
