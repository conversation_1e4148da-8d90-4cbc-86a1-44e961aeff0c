{% extends "base.html" %}

{% block title %}成就系统 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-trophy-fill text-warning me-2"></i>成就系统</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 成就概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-graph-up text-primary me-2"></i>成就概况
                </h5>
                <div class="row text-center">
                    {% set total_achievements = achievements|length %}
                    {% set achieved_count = achievements|selectattr("achieved")|list|length %}
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-check-circle-fill text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">{{ achieved_count }}</h4>
                            <small class="text-muted">已解锁成就</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-trophy-fill text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">{{ total_achievements }}</h4>
                            <small class="text-muted">成就总数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-pie-chart-fill text-warning fs-3 mb-2"></i>
                            <h4 class="text-warning mb-1">{{ "%.1f"|format((achieved_count / total_achievements * 100) if total_achievements > 0 else 0) }}%</h4>
                            <small class="text-muted">完成度</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-award-fill text-info fs-3 mb-2"></i>
                            <h4 class="text-info mb-1">{{ achieved_count * 100 }}</h4>
                            <small class="text-muted">获得声望</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成就分类 -->
{% for category, achievements in achievement_groups.items() %}
{% if achievements %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    {% if category == '财务成就' %}
                    <i class="bi bi-cash-stack text-success me-2"></i>
                    {% elif category == '员工成就' %}
                    <i class="bi bi-people-fill text-info me-2"></i>
                    {% elif category == '发展成就' %}
                    <i class="bi bi-arrow-up-circle text-primary me-2"></i>
                    {% elif category == '经营成就' %}
                    <i class="bi bi-graph-up text-warning me-2"></i>
                    {% else %}
                    <i class="bi bi-star-fill text-secondary me-2"></i>
                    {% endif %}
                    {{ category }}
                </h5>
                
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="15%">成就类型</th>
                                <th width="25%">成就名称</th>
                                <th width="35%">成就描述</th>
                                <th width="10%">达成奖励</th>
                                <th width="15%">操作按钮</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for achievement in achievements %}
                            <tr class="{% if achievement.achieved %}table-success{% endif %}">
                                <td>
                                    <span class="badge bg-primary">{{ achievement.category }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if achievement.achieved %}
                                            {% if achievement.reward_claimed %}
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            {% else %}
                                            <i class="bi bi-gift-fill text-warning me-2"></i>
                                            {% endif %}
                                        {% else %}
                                        <i class="bi bi-circle text-muted me-2"></i>
                                        {% endif %}
                                        <strong>{{ achievement.name }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">{{ achievement.description }}</small>
                                </td>

                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        {% if achievement.reward_money > 0 %}
                                        <span class="badge bg-success">+¥{{ "{:,}".format(achievement.reward_money) }}</span>
                                        {% endif %}
                                        {% if achievement.reward_reputation > 0 %}
                                        <span class="badge bg-info">+{{ achievement.reward_reputation }}声望</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if achievement.achieved %}
                                        {% if achievement.reward_claimed %}
                                        <span class="badge bg-success">已解锁</span>
                                        {% else %}
                                        <button class="btn btn-sm btn-warning" onclick="claimReward({{ achievement.id }})">
                                            <i class="bi bi-gift me-1"></i>领取
                                        </button>
                                        {% endif %}
                                    {% else %}
                                    <span class="badge bg-secondary">未达成</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}

{% if not achievement_groups or achievement_groups|length == 0 %}
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <i class="bi bi-trophy display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无成就</h4>
                <p class="text-muted">成就系统正在初始化中...</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-1"></i>刷新页面
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function claimReward(achievementId) {
    if (confirm('确定要领取这个成就的奖励吗？')) {
        apiRequest('/achievements/claim', {
            method: 'POST',
            body: JSON.stringify({achievement_id: achievementId})
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('领取奖励失败', 'error');
        });
    }
}
</script>
{% endblock %}
