{% extends "base.html" %}

{% block title %}房间管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-door-open-fill text-info me-2"></i>房间管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 房间概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-graph-up text-primary me-2"></i>房间概况
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-door-open-fill text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">{{ total_rooms }}</h4>
                            <small class="text-muted">房间总数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-person-check-fill text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">{{ occupied_rooms }}</h4>
                            <small class="text-muted">已入住</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-graph-up text-info fs-3 mb-2"></i>
                            <h4 class="text-info mb-1">{{ "%.1f"|format(occupancy_rate) }}%</h4>
                            <small class="text-muted">入住率</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-tools text-warning fs-3 mb-2"></i>
                            <h4 class="text-warning mb-1">¥{{ "{:,}".format(monthly_maintenance) }}</h4>
                            <small class="text-muted">月维护费</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 房间类型管理 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="bi bi-building text-primary me-2"></i>房间类型管理
                    </h5>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="15%">房间类型</th>
                                <th width="10%">数量</th>
                                <th width="12%">单价</th>
                                <th width="12%">建设费用</th>
                                <th width="12%">入住率</th>
                                <th width="12%">状态</th>
                                <th width="27%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for room_type, rooms in rooms_by_type.items() %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-house-door text-primary me-2"></i>
                                        <strong>{{ room_type }}</strong>
                                    </div>
                                </td>
                                <td>
                                    {% if rooms %}
                                    <span class="badge bg-primary fs-6">{{ rooms[0].count }}间</span>
                                    {% else %}
                                    <span class="text-muted">0间</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-success fw-bold">¥{{ "{:,}".format(room_prices[room_type]) }}/晚</span>
                                </td>
                                <td>
                                    <span class="text-warning fw-bold">¥{{ "{:,}".format(room_build_costs[room_type]) }}/间</span>
                                </td>

                                <td>
                                    {% if rooms %}
                                    <div class="d-flex align-items-center">
                                        <div class="progress me-2" style="width: 80px; height: 8px;">
                                            <div class="progress-bar bg-success" style="width: {{ room_occupancy_rates.get(room_type, 0) }}%"></div>
                                        </div>
                                        <small>{{ "%.1f"|format(room_occupancy_rates.get(room_type, 0)) }}%</small>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if rooms %}
                                    <span class="badge bg-success">运营中</span>
                                    {% elif hotel.level >= room_requirements.get(room_type, 1) %}
                                    <span class="badge bg-warning">可解锁</span>
                                    {% else %}
                                    <span class="badge bg-secondary">需{{ room_requirements.get(room_type, 1) }}星</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if rooms %}
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="addRooms('{{ room_type }}', 1)" title="新增1间">
                                            <i class="bi bi-plus"></i> 1
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="addRooms('{{ room_type }}', 10)" title="新增10间">
                                            <i class="bi bi-plus"></i> 10
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="addRooms('{{ room_type }}', 50)" title="新增50间">
                                            <i class="bi bi-plus"></i> 50
                                        </button>
                                    </div>
                                    {% elif hotel.level >= room_requirements.get(room_type, 1) %}
                                    <button class="btn btn-sm btn-success" onclick="unlockRoomType('{{ room_type }}')" title="解锁房型">
                                        <i class="bi bi-unlock-fill"></i> 解锁
                                    </button>
                                    {% else %}
                                    <span class="text-muted">--</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function addRooms(roomType, quantity) {
    if (confirm(`确定要新增${quantity}间${roomType}吗？\n建设费用：¥${(getRoomPrice(roomType) * 10 * quantity).toLocaleString()}`)) {
        apiRequest('/rooms/add', {
            method: 'POST',
            body: JSON.stringify({
                room_type: roomType,
                quantity: quantity
            })
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('新增房间失败', 'error');
        });
    }
}

function unlockRoomType(roomType) {
    if (confirm(`确定要解锁${roomType}吗？`)) {
        apiRequest('/rooms/unlock', {
            method: 'POST',
            body: JSON.stringify({
                room_type: roomType
            })
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('解锁房型失败', 'error');
        });
    }
}

function getRoomPrice(roomType) {
    const prices = {
        '单人间': 300, '标准间': 500, '大床房': 700, '家庭房': 1000,
        '商务间': 1500, '行政间': 2000, '豪华间': 3000, '总统套房': 5000,
        '皇家套房': 8000, '总统别墅': 15000, '皇宫套房': 30000
    };
    return prices[roomType] || 300;
}
</script>
{% endblock %}
