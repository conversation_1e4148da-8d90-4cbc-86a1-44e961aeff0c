{% extends "base.html" %}

{% block title %}营销管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-megaphone-fill text-primary me-2"></i>营销管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 营销概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-graph-up text-primary me-2"></i>营销概况
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-play-circle-fill text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">{{ active_campaigns }}</h4>
                            <small class="text-muted">活跃营销活动</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-cash-stack text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">¥{{ "{:,}".format(monthly_marketing_budget) }}</h4>
                            <small class="text-muted">月度营销预算</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-graph-up-arrow text-info fs-3 mb-2"></i>
                            <h4 class="text-info mb-1">+{{ "%.1f"|format(marketing_effect) }}%</h4>
                            <small class="text-muted">入住率提升</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-award-fill text-warning fs-3 mb-2"></i>
                            <h4 class="text-warning mb-1">{{ brand_awareness }}%</h4>
                            <small class="text-muted">品牌知名度</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 营销活动 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-bullseye text-primary me-2"></i>营销活动
                </h5>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="20%">营销活动</th>
                                <th width="25%">描述</th>
                                <th width="12%">费用</th>
                                <th width="10%">效果</th>
                                <th width="10%">持续</th>
                                <th width="13%">状态</th>
                                <th width="10%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for campaign in marketing_campaigns %}
                            <tr class="{% if campaign.is_active %}table-success{% endif %}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-megaphone text-primary me-2"></i>
                                        <strong>{{ campaign.name }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">{{ campaign.description }}</span>
                                </td>
                                <td>
                                    {% if campaign.cost >= 10000000 %}
                                    <span class="text-warning fw-bold">{{ (campaign.cost // 10000) }}万</span>
                                    {% elif campaign.cost >= 10000 %}
                                    <span class="text-warning fw-bold">{{ (campaign.cost // 10000) }}万</span>
                                    {% else %}
                                    <span class="text-warning fw-bold">¥{{ "{:,}".format(campaign.cost) }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-success">+{{ campaign.effect }}%</span>
                                </td>
                                <td>
                                    <span class="text-info">{{ campaign.duration }}天</span>
                                </td>
                                <td>
                                    {% if campaign.is_active %}
                                    <div>
                                        <span class="badge bg-success">进行中</span>
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-success" style="width: {{ campaign.progress }}%"></div>
                                        </div>
                                        <small class="text-muted">剩余{{ campaign.remaining_days }}天</small>
                                    </div>
                                    {% else %}
                                    <span class="badge bg-secondary">待启动</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if campaign.is_active %}
                                    <button class="btn btn-sm btn-outline-danger" onclick="stopCampaign('{{ campaign.id }}')">
                                        <i class="bi bi-stop-fill"></i>
                                    </button>
                                    {% elif hotel.money >= campaign.cost %}
                                    <button class="btn btn-sm btn-success" onclick="startCampaign('{{ campaign.id }}')">
                                        <i class="bi bi-play-fill"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-sm btn-secondary" disabled>
                                        <i class="bi bi-cash"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 营销历史 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-clock-history text-primary me-2"></i>营销历史
                </h5>

                {% if marketing_history %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>活动名称</th>
                                <th>开始日期</th>
                                <th>结束日期</th>
                                <th>费用</th>
                                <th>效果</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in marketing_history %}
                            <tr>
                                <td>{{ record.name }}</td>
                                <td>{{ record.started_at.strftime('%Y-%m-%d') if record.started_at else '-' }}</td>
                                <td>
                                    {% if record.end_date %}
                                    {{ record.end_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                    <span class="text-muted">进行中</span>
                                    {% endif %}
                                </td>
                                <td>¥{{ "{:,}".format(record.cost) }}</td>
                                <td>+{{ record.effect }}% 入住率</td>
                                <td>
                                    {% if record.is_active %}
                                    <span class="badge bg-success">进行中</span>
                                    {% else %}
                                    <span class="badge bg-secondary">已结束</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-megaphone display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无营销记录</h4>
                    <p class="text-muted">启动营销活动来提升酒店知名度和入住率</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function startCampaign(campaignId) {
    if (confirm('确定要启动这个营销活动吗？')) {
        apiRequest('/marketing/start_campaign', {
            method: 'POST',
            body: JSON.stringify({campaign_id: campaignId})
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('启动营销活动失败', 'error');
        });
    }
}

function stopCampaign(campaignId) {
    if (confirm('确定要停止这个营销活动吗？已支付的费用不会退还。')) {
        apiRequest('/marketing/stop_campaign', {
            method: 'POST',
            body: JSON.stringify({campaign_id: campaignId})
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('停止营销活动失败', 'error');
        });
    }
}
</script>
{% endblock %}
