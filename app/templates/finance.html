{% extends "base.html" %}

{% block title %}财务管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-cash-stack text-warning me-2"></i>财务管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 财务概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-graph-up text-primary me-2"></i>财务概况
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-cash-stack text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">¥{{ "{:,}".format(hotel.money) }}</h4>
                            <small class="text-muted">当前资金</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-arrow-up-circle text-info fs-3 mb-2"></i>
                            <h4 class="text-info mb-1">¥{{ "{:,}".format(daily_income) }}</h4>
                            <small class="text-muted">日收入</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-arrow-down-circle text-warning fs-3 mb-2"></i>
                            <h4 class="text-warning mb-1">¥{{ "{:,}".format(daily_expense) }}</h4>
                            <small class="text-muted">日支出</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-graph-up text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">¥{{ "{:,}".format(daily_profit) }}</h4>
                            <small class="text-muted">日利润</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 收入分析 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="bi bi-arrow-up-circle text-success me-2"></i>收入来源
                </h6>
                <div class="list-group list-group-flush">
                    {% for income_type, amount in income_breakdown.items() %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <span>{{ income_type }}</span>
                        <span class="text-success fw-bold">+¥{{ "{:,}".format(amount) }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="bi bi-arrow-down-circle text-danger me-2"></i>支出明细
                </h6>
                <div class="list-group list-group-flush">
                    {% for expense_type, amount in expense_breakdown.items() %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <span>{{ expense_type }}</span>
                        <span class="text-danger fw-bold">-¥{{ "{:,}".format(amount) }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 财务记录 -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="bi bi-journal-text text-primary me-2"></i>财务记录
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="filterRecords('all')">全部</button>
                        <button class="btn btn-outline-success" onclick="filterRecords('income')">收入</button>
                        <button class="btn btn-outline-danger" onclick="filterRecords('expense')">支出</button>
                    </div>
                </div>

                {% if financial_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>类型</th>
                                <th>描述</th>
                                <th class="text-end">金额</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTable">
                            {% for record in financial_records %}
                            <tr class="record-row" data-type="{{ 'income' if record.income > 0 else 'expense' }}">
                                <td>{{ record.record_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if record.income > 0 %}
                                    <span class="badge bg-success">收入</span>
                                    {% else %}
                                    <span class="badge bg-danger">支出</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.description }}</td>
                                <td class="text-end">
                                    {% if record.income > 0 %}
                                    <span class="text-success fw-bold">+¥{{ "{:,}".format(record.income) }}</span>
                                    {% else %}
                                    <span class="text-danger fw-bold">-¥{{ "{:,}".format(record.expense) }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <nav class="mt-3">
                    <ul class="pagination pagination-sm justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('finance.management', page=pagination.prev_num) }}">上一页</a>
                        </li>
                        {% endif %}

                        {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != pagination.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('finance.management', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('finance.management', page=pagination.next_num) }}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-journal display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无财务记录</h4>
                    <p class="text-muted">财务记录将在酒店运营过程中自动生成</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function filterRecords(type) {
    const rows = document.querySelectorAll('.record-row');
    const buttons = document.querySelectorAll('.btn-group .btn');

    // 更新按钮状态
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // 过滤记录
    rows.forEach(row => {
        if (type === 'all' || row.dataset.type === type) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
</script>
{% endblock %}
