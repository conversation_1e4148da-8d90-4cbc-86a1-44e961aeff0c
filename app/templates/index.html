{% extends "base.html" %}

{% block title %}{{ hotel.name }} - 酒店管理系统{% endblock %}

{% block content %}
<!-- 顶部信息栏 - 参考若依设计风格 -->
<div class="row mb-1">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="bi bi-building-fill text-white fs-5"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 text-dark d-flex align-items-center">
                                    <span id="hotelName" style="cursor: pointer;" onclick="editHotelName()">{{ hotel.name or '红珊瑚大酒店' }}</span>
                                    <i class="bi bi-pencil-square text-muted ms-2" style="font-size: 0.8rem; cursor: pointer;" onclick="editHotelName()" title="点击修改酒店名称"></i>
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-md-end">
                            <div class="d-inline-flex align-items-center bg-light rounded px-3 py-2">
                                <i class="bi bi-calendar3 text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold text-dark" id="currentDate">{{ hotel.date.strftime('%Y-%m-%d') if hotel.date else '1990-01-01' }}</div>
                                    <small class="text-muted">运营第 <span id="daysElapsed">{{ hotel.days_elapsed or 0 }}</span> 天</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据统计卡片 - 参考若依设计风格 -->
<div class="row mb-1">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-star-fill text-primary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <p class="text-muted mb-1">酒店星级</p>
                        <h4 class="mb-0" id="hotelLevel">
                            {% for i in range(hotel.level) %}
                            <i class="bi bi-star-fill text-warning"></i>
                            {% endfor %}
                            {% for i in range(9 - hotel.level) %}
                            <i class="bi bi-star text-muted"></i>
                            {% endfor %}
                        </h4>
                        <small class="text-muted">{{ hotel.level }}星级酒店</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-cash-stack text-success me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <p class="text-muted mb-1 me-2">资金余额</p>
                            <button class="btn btn-outline-info p-1" style="font-size: 0.75rem; line-height: 1;" data-bs-toggle="modal" data-bs-target="#financeHelpModal" title="资金计算说明">
                                <i class="bi bi-question-circle" style="font-size: 0.75rem;"></i>
                            </button>
                        </div>
                        <h4 class="mb-0 text-success" id="hotelMoney">¥{{ "{:,}".format(hotel.money) }}</h4>
                        <small class="text-muted">可用资金</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-heart-fill text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <p class="text-muted mb-1 me-2">客户满意度</p>
                            <button class="btn btn-outline-info p-1" style="font-size: 0.75rem; line-height: 1;" data-bs-toggle="modal" data-bs-target="#satisfactionHelpModal" title="满意度计算说明">
                                <i class="bi bi-question-circle" style="font-size: 0.75rem;"></i>
                            </button>
                        </div>
                        <div class="d-flex align-items-center">
                            <h4 class="mb-0 me-2" id="satisfaction">{{ "%.0f"|format(hotel.satisfaction) }}分</h4>
                            <div class="progress flex-grow-1" style="height: 6px;">
                                <div class="progress-bar bg-danger" style="width: {{ hotel.satisfaction }}%" id="satisfactionBar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-trophy-fill text-warning me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <p class="text-muted mb-1 me-2">声望值</p>
                            <button class="btn btn-outline-info p-1" style="font-size: 0.75rem; line-height: 1;" data-bs-toggle="modal" data-bs-target="#reputationHelpModal" title="声望计算说明">
                                <i class="bi bi-question-circle" style="font-size: 0.75rem;"></i>
                            </button>
                        </div>
                        <h4 class="mb-0" id="reputation">{{ hotel.reputation }}</h4>
                        <div class="d-flex align-items-center">
                            <small class="text-muted" id="reputationDesc">默默无闻</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 运营数据卡片 -->
<div class="row mb-1">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-door-open-fill text-info me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <p class="text-muted mb-1">房间总数</p>
                        <h4 class="mb-0" id="totalRooms">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-person-badge-fill text-secondary me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <p class="text-muted mb-1">员工总数</p>
                        <h4 class="mb-0" id="totalEmployees">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-people-fill text-danger me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <p class="text-muted mb-1">入住客户</p>
                        <h4 class="mb-0" id="totalGuests">0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="bi bi-currency-dollar text-warning me-3 fs-4"></i>
                    <div class="flex-grow-1">
                        <p class="text-muted mb-1">月度利润</p>
                        <h4 class="mb-0 text-warning" id="monthlyProfit">¥0</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 主要内容区域 - 参考若依布局 -->
<div class="row">
    <!-- 左侧：图表区域 -->
    <div class="col-lg-8">
        <!-- 财务记录汇总表 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex align-items-center justify-content-between">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table text-success me-2"></i>财务记录汇总
                    </h5>
                    <small class="text-muted">最近12个月</small>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover" id="financialSummaryTable">
                        <thead class="table-light">
                        <tr>
                            <th class="text-center">月份</th>
                            <th class="text-end">总收入</th>
                            <th class="text-end">总支出</th>
                            <th class="text-end">净利润</th>
                            <th class="text-center">状态</th>
                        </tr>
                        </thead>
                        <tbody id="financialSummaryBody">
                        <!-- 动态加载财务数据 -->
                        <tr>
                            <td colspan="5" class="text-center text-muted">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                加载中...
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-2">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        数据按月汇总，当月数据为截至当前日期的累计值
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧：控制面板 -->
    <div class="col-lg-4 d-flex flex-column">
        <!-- 游戏控制面板 - 合并时间控制和游戏管理 -->
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear text-primary me-2"></i>游戏控制
                </h5>
            </div>
            <div class="card-body">
                <!-- 时间状态显示 - 简化版 -->
                <div class="row g-1 mb-2">
                    <div class="col-6">
                        <div class="text-center p-1 bg-light rounded">
                            <span class="badge bg-success" id="timeStatus" style="font-size: 0.7rem;">{{ '运行中' if hotel.time_running else '已暂停' }}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-1 bg-light rounded">
                            <span class="badge bg-primary" id="timeSpeed" style="font-size: 0.7rem;">{{ hotel.time_speed or 1 }}倍速</span>
                        </div>
                    </div>
                </div>

                <!-- 游戏控制按钮 -->
                <div class="row g-1 mb-2">
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-warning w-100" id="toggleTimeBtn" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-pause-fill me-1" style="font-size: 0.7rem;"></i>暂停
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-primary w-100" id="toggleSpeedBtn" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-speedometer2 me-1" style="font-size: 0.7rem;"></i>速度
                        </button>
                    </div>
                </div>

                <!-- 存档管理 -->
                <div class="row g-1 mb-2">
                    <div class="col-4">
                        <button type="button" class="btn btn-outline-success w-100" onclick="showSaveSlots()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-save me-1" style="font-size: 0.7rem;"></i>存档
                        </button>
                    </div>
                    <div class="col-4">
                        <button type="button" class="btn btn-outline-info w-100" onclick="showLoadSlots()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-folder-open me-1" style="font-size: 0.7rem;"></i>读档
                        </button>
                    </div>
                    <div class="col-4">
                        <button type="button" class="btn btn-outline-danger w-100" onclick="restartGame()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-arrow-clockwise me-1" style="font-size: 0.7rem;"></i>重开
                        </button>
                    </div>
                </div>

                <!-- 帮助按钮 -->
                <div class="row g-1">
                    <div class="col-12">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="showHelp()" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-question-circle me-1" style="font-size: 0.7rem;"></i>游戏帮助
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷管理面板 - 参考若依风格 -->
        <div class="card border-0 shadow-sm flex-fill">
            <div class="card-header bg-white border-bottom">
                <h5 class="card-title mb-0">
                    <i class="bi bi-grid-3x3-gap text-primary me-2"></i>快捷管理
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-1">
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="showManagementModal('departments')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-diagram-3-fill me-1" style="font-size: 0.7rem;"></i>部门
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-success w-100" onclick="showManagementModal('employees')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-people-fill me-1" style="font-size: 0.7rem;"></i>员工
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-info w-100" onclick="showManagementModal('rooms')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-door-open-fill me-1" style="font-size: 0.7rem;"></i>房间
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-warning w-100" onclick="showManagementModal('finance')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-cash-stack me-1" style="font-size: 0.7rem;"></i>财务
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="showManagementModal('hotel')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-arrow-up-circle-fill me-1" style="font-size: 0.7rem;"></i>升级
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-danger w-100" onclick="showManagementModal('marketing')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-megaphone-fill me-1" style="font-size: 0.7rem;"></i>营销
                        </button>
                    </div>
                    <div class="col-6">
                        <button type="button" class="btn btn-outline-success w-100" onclick="showManagementModal('achievements')" style="font-size: 0.7rem; padding: 0.2rem 0.4rem; height: 28px;">
                            <i class="bi bi-trophy-fill me-1" style="font-size: 0.7rem;"></i>成就
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 存档卡槽模态框 -->
<div class="modal fade" id="saveSlotModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择存档卡槽</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-2">
                    <div class="col-12" id="saveSlot1">
                        <div class="card border">
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">存档槽 1</h6>
                                        <small class="text-muted" id="slot1Info">空存档</small>
                                    </div>
                                    <button class="btn btn-sm btn-primary" onclick="saveToSlot(1)">保存</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12" id="saveSlot2">
                        <div class="card border">
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">存档槽 2</h6>
                                        <small class="text-muted" id="slot2Info">空存档</small>
                                    </div>
                                    <button class="btn btn-sm btn-primary" onclick="saveToSlot(2)">保存</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12" id="saveSlot3">
                        <div class="card border">
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">存档槽 3</h6>
                                        <small class="text-muted" id="slot3Info">空存档</small>
                                    </div>
                                    <button class="btn btn-sm btn-primary" onclick="saveToSlot(3)">保存</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 读取存档模态框 -->
<div class="modal fade" id="loadSlotModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择读取存档</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-2" id="loadSlotContainer">
                    <!-- 动态加载存档信息 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 若依风格的自定义CSS -->
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }

    .card {
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
    }

    .btn {
        border-radius: 6px;
        font-weight: 500;
    }

    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-info:hover,
    .btn-outline-warning:hover,
    .btn-outline-danger:hover,
    .btn-outline-secondary:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    /* 统一小按钮样式优化 */
    .btn[style*="height: 28px"] {
        line-height: 1.2;
        border-width: 1px;
        min-width: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn[style*="height: 28px"]:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .btn[style*="height: 28px"] i {
        vertical-align: baseline;
    }

    /* 铺满整屏样式 */
    html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        min-height: 100vh;
        overflow-x: hidden;
    }

    .container-fluid {
        max-width: 100%;
        padding-left: 4px;
        padding-right: 4px;
        margin: 0;
    }

    /* 移除所有默认边距 */
    .row {
        margin-left: 0;
        margin-right: 0;
    }

    .col, .col-lg-4, .col-lg-8, .col-xl-3, .col-md-6 {
        padding-left: 2px;
        padding-right: 2px;
    }

    /* 右侧面板高度对齐 */
    .col-lg-4.d-flex.flex-column {
        height: 100%;
    }

    .col-lg-4 .card.flex-fill {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .col-lg-4 .card.flex-fill .card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .progress {
        border-radius: 10px;
    }

    .badge {
        font-weight: 500;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .bg-light {
        background-color: #f8f9fa !important;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 1.25rem;
    }

    .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .avatar-sm {
            width: 35px;
            height: 35px;
        }

        .card-title {
            font-size: 1rem;
        }

        h4 {
            font-size: 1.1rem;
        }
    }
</style>

<script>
    // 通用API请求函数（全局可用）
    window.apiRequest = async function(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    };

    // 游戏状态管理
    let gameState = {
        isTimeRunning: true,
        timeSpeed: 1,
        updateInterval: null,
        lastUpdateTime: 0,
        chart: null,
        baseRefreshRate: 5000,  // 基础刷新频率（毫秒）
        minRefreshRate: 500,    // 最小刷新频率（毫秒）
        maxRefreshRate: 10000   // 最大刷新频率（毫秒）
    };

    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 酒店管理系统初始化...');



        // 立即更新数据
        updateAllData();

        // 设置动态刷新频率（根据时间倍速调整）
        startDynamicRefresh();

        // 绑定事件处理器
        bindEventHandlers();

        // 启动事件检查系统
        startEventSystem();

        console.log('✅ 系统初始化完成');
    });

    // 动态刷新频率管理
    function startDynamicRefresh() {
        // 清除现有的定时器
        if (gameState.updateInterval) {
            clearInterval(gameState.updateInterval);
        }

        // 根据时间倍速计算刷新频率
        function calculateRefreshRate() {
            // 基础公式：刷新频率 = 基础频率 / 时间倍速
            let refreshRate = gameState.baseRefreshRate / gameState.timeSpeed;

            // 限制在最小和最大频率之间
            refreshRate = Math.max(gameState.minRefreshRate, refreshRate);
            refreshRate = Math.min(gameState.maxRefreshRate, refreshRate);

            return refreshRate;
        }

        // 设置动态定时器
        function setDynamicInterval() {
            const refreshRate = calculateRefreshRate();
            console.log(`🔄 设置刷新频率: ${refreshRate}ms (倍速: ${gameState.timeSpeed}x)`);

            gameState.updateInterval = setInterval(() => {
                updateAllData();

                // 检查倍速是否改变，如果改变则重新设置定时器
                const newRefreshRate = calculateRefreshRate();
                if (Math.abs(newRefreshRate - refreshRate) > 100) {
                    setDynamicInterval();
                }
            }, refreshRate);
        }

        setDynamicInterval();
    }

    // 更新时间倍速时重新设置刷新频率
    function updateRefreshRate() {
        if (gameState.isTimeRunning) {
            startDynamicRefresh();
        }
    }

    // 根据声望值获取对应描述
    function getReputationDescription(reputation) {
        if (reputation >= 100000) return '举世闻名';
        if (reputation >= 50000) return '声名远扬';
        if (reputation >= 25000) return '名声显赫';
        if (reputation >= 12000) return '小有名气';
        if (reputation >= 6000) return '略有声望';
        if (reputation >= 3000) return '初露头角';
        if (reputation >= 1500) return '崭露头角';
        if (reputation >= 500) return '小试牛刀';
        return '默默无闻';
    }

    // 绑定所有事件处理器
    function bindEventHandlers() {
        // 时间控制按钮
        document.getElementById('toggleTimeBtn')?.addEventListener('click', toggleTime);
        document.getElementById('toggleSpeedBtn')?.addEventListener('click', toggleTimeSpeed);
    }

    // 更新所有数据
    async function updateAllData() {
        try {
            await Promise.all([
                updateHotelInfo(),
                updateFinancialSummary(),
                checkEvents()
            ]);

            gameState.lastUpdateTime = Date.now();
            console.log('📊 数据更新完成');
        } catch (error) {
            console.error('❌ 数据更新失败:', error);
        }
    }

    // 更新酒店信息
    async function updateHotelInfo() {
        try {
            const data = await apiRequest('/api/hotel_info');

            if (data.success) {
                // 更新基本信息
                updateElement('hotelName', data.hotel_name);
                // 更新星级显示
                let starsHtml = '';
                for (let i = 0; i < data.level; i++) {
                    starsHtml += '<i class="bi bi-star-fill text-warning"></i>';
                }
                for (let i = data.level; i < 9; i++) {
                    starsHtml += '<i class="bi bi-star text-muted"></i>';
                }
                document.getElementById('hotelLevel').innerHTML = starsHtml;
                updateElement('hotelMoney', formatCurrency(data.money));
                updateElement('currentDate', data.current_date);
                updateElement('daysElapsed', data.days_elapsed);
                updateElement('reputation', data.reputation);

                // 根据声望值显示对应描述
                let reputationDesc = getReputationDescription(data.reputation);
                updateElement('reputationDesc', reputationDesc);

                updateElement('satisfaction', data.satisfaction.toFixed(1) + '分');

                // 更新运营概览
                updateElement('totalRooms', data.total_rooms || 0);
                updateElement('totalGuests', Math.round(data.total_guests || 0));
                updateElement('totalEmployees', data.employee_count || 0);
                updateElement('monthlyProfit', formatCurrency(data.monthly_profit || 0));

                // 更新时间状态
                gameState.isTimeRunning = data.time_running;
                gameState.timeSpeed = data.time_speed || 1;
                updateTimeControls();

                // 更新满意度进度条
                updateSatisfactionBar(data.satisfaction);
            }
        } catch (error) {
            console.error('获取酒店信息失败:', error);
        }
    }

    // 更新时间控制显示
    function updateTimeControls() {
        const timeStatus = document.getElementById('timeStatus');
        const timeSpeed = document.getElementById('timeSpeed');
        const toggleBtn = document.getElementById('toggleTimeBtn');

        if (timeStatus) {
            timeStatus.textContent = gameState.isTimeRunning ? '运行中' : '已暂停';
            timeStatus.className = 'badge ' + (gameState.isTimeRunning ? 'bg-success' : 'bg-danger');
        }

        if (timeSpeed) {
            timeSpeed.textContent = gameState.timeSpeed + '倍速';
        }

        if (toggleBtn) {
            toggleBtn.innerHTML = gameState.isTimeRunning ?
                '<i class="bi bi-pause-fill"></i>' :
                '<i class="bi bi-play-fill"></i>';
            toggleBtn.title = gameState.isTimeRunning ? '暂停时间' : '继续时间';
        }
    }

    // 更新满意度进度条
    function updateSatisfactionBar(satisfaction) {
        const bar = document.getElementById('satisfactionBar');
        if (bar) {
            bar.style.width = satisfaction + '%';
            bar.setAttribute('aria-valuenow', satisfaction);
        }
    }





    // 时间控制函数
    async function toggleTime() {
        try {
            const data = await apiRequest('/api/toggle_time', { method: 'POST' });
            if (data.success) {
                showMessage(data.message, 'success');
                await updateAllData();
            }
        } catch (error) {
            showMessage('时间控制操作失败', 'error');
        }
    }

    async function toggleTimeSpeed() {
        try {
            const data = await apiRequest('/api/toggle_time_speed', { method: 'POST' });
            if (data.success) {
                // 更新游戏状态中的时间倍速
                gameState.timeSpeed = data.time_speed || 1;

                showMessage(data.message, 'success');
                await updateAllData();

                // 更新刷新频率
                updateRefreshRate();
            }
        } catch (error) {
            showMessage('速度切换操作失败', 'error');
        }
    }

    // 游戏管理函数
    function editHotelName() {
        const currentName = document.getElementById('hotelName').textContent;
        const newName = prompt('请输入新的酒店名称:', currentName);

        if (newName && newName.trim() && newName.trim() !== currentName) {
            apiRequest('/api/update_hotel_name', {
                method: 'POST',
                body: JSON.stringify({name: newName.trim()})
            }).then(data => {
                if (data.success) {
                    updateElement('hotelName', newName.trim());
                    showMessage('酒店名称已更新', 'success');
                }
            }).catch(() => {
                showMessage('更新酒店名称失败', 'error');
            });
        }
    }

    function showSaveSlots() {
        // 加载存档信息
        loadSaveSlotInfo();
        const modal = new bootstrap.Modal(document.getElementById('saveSlotModal'));
        modal.show();
    }

    function showLoadSlots() {
        // 加载存档信息
        loadSaveSlotInfo();
        loadLoadSlotInfo();
        const modal = new bootstrap.Modal(document.getElementById('loadSlotModal'));
        modal.show();
    }

    function loadSaveSlotInfo() {
        // 加载存档槽信息
        apiRequest('/api/get_save_slots', { method: 'GET' })
        .then(data => {
            if (data.success) {
                for (let i = 1; i <= 3; i++) {
                    const slotInfo = data.slots[`slot${i}`];
                    const infoElement = document.getElementById(`slot${i}Info`);
                    if (slotInfo) {
                        infoElement.innerHTML = `${slotInfo.hotel_name}<br><small>${slotInfo.date} - ${slotInfo.level}星 - ¥${slotInfo.money.toLocaleString()}</small>`;
                    } else {
                        infoElement.textContent = '空存档';
                    }
                }
            }
        }).catch(() => {
            showMessage('加载存档信息失败', 'error');
        });
    }

    function loadLoadSlotInfo() {
        // 为读取模态框加载存档信息
        apiRequest('/api/get_save_slots', { method: 'GET' })
        .then(data => {
            if (data.success) {
                const container = document.getElementById('loadSlotContainer');
                container.innerHTML = '';

                for (let i = 1; i <= 3; i++) {
                    const slotInfo = data.slots[`slot${i}`];
                    const slotHtml = `
                        <div class="col-12">
                            <div class="card border">
                                <div class="card-body p-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">存档槽 ${i}</h6>
                                            <small class="text-muted">${slotInfo ? `${slotInfo.hotel_name} - ${slotInfo.date} - ${slotInfo.level}星 - ¥${slotInfo.money.toLocaleString()}` : '空存档'}</small>
                                        </div>
                                        <button class="btn btn-sm ${slotInfo ? 'btn-success' : 'btn-secondary'}"
                                                onclick="loadFromSlot(${i})"
                                                ${!slotInfo ? 'disabled' : ''}>
                                            ${slotInfo ? '读取' : '空'}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    container.innerHTML += slotHtml;
                }
            }
        }).catch(() => {
            showMessage('加载存档信息失败', 'error');
        });
    }

    function saveToSlot(slotNumber) {
        const slotInfo = document.getElementById(`slot${slotNumber}Info`).textContent;
        if (slotInfo !== '空存档') {
            if (!confirm(`存档槽 ${slotNumber} 已有存档，确定要覆盖吗？`)) {
                return;
            }
        }

        apiRequest('/api/save_to_slot', {
            method: 'POST',
            body: JSON.stringify({slot: slotNumber})
        }).then(data => {
            if (data.success) {
                showMessage(`保存到存档槽 ${slotNumber} 成功`, 'success');
                loadSaveSlotInfo(); // 刷新存档信息
                bootstrap.Modal.getInstance(document.getElementById('saveSlotModal')).hide();
            }
        }).catch(() => {
            showMessage('保存失败', 'error');
        });
    }

    function loadFromSlot(slotNumber) {
        if (!confirm(`确定要读取存档槽 ${slotNumber} 吗？当前进度将会丢失！`)) {
            return;
        }

        apiRequest('/api/load_from_slot', {
            method: 'POST',
            body: JSON.stringify({slot: slotNumber})
        }).then(data => {
            if (data.success) {
                showMessage('读取存档成功，即将刷新页面...', 'success');
                setTimeout(() => location.reload(), 2000);
            }
        }).catch(() => {
            showMessage('读取存档失败', 'error');
        });
    }

    function restartGame() {
        confirmAction('确定要重新开始游戏吗？当前进度将会完全丢失！', () => {
            apiRequest('/api/restart_game', { method: 'POST' })
            .then(data => {
                if (data.success) {
                    showMessage('游戏重新开始，即将刷新页面...', 'success');
                    setTimeout(() => location.reload(), 2000);
                }
            }).catch(() => {
                showMessage('重新开始游戏失败', 'error');
            });
        });
    }

    function showHelp() {
        const helpText = `酒店管理系统 - 游戏帮助

🎮 基本操作：
• 时间控制：可以暂停/继续时间，切换1倍速/2倍速
• 手动推进：点击推进按钮手动推进时间

🏨 酒店管理：
• 升级条件：需要满足资金、声望、运营天数、满意度要求
• 部门效果：不同部门解锁后有特殊效果和收入加成
• 满意度：影响声望值变化和客户入住意愿

💰 经营策略：
• 合理招聘员工，避免部门过度繁忙
• 投资营销活动提升入住率
• 平衡收入和支出，确保资金流健康

🏆 成就系统：
• 完成各种成就可获得声望值奖励
• 声望等级影响入住率和随机事件

💾 存档功能：
• 支持保存和读取游戏进度
• 可以导出游戏数据进行备份`;

        alert(helpText);
    }

    function exportData() {
        showMessage('数据导出功能开发中...', 'info');
    }

    function showSettings() {
        showMessage('游戏设置功能开发中...', 'info');
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (gameState.updateInterval) {
            clearInterval(gameState.updateInterval);
        }
    });

    // 事件弹窗系统
    function startEventSystem() {
        // 每2分钟检查一次随机事件
        setInterval(checkRandomEvents, 120000);

        // 每5分钟检查一次月底事件
        setInterval(checkMonthlyEvents, 300000);

        // 页面加载时延迟检查事件
        setTimeout(checkRandomEvents, 10000);
        setTimeout(checkMonthlyEvents, 15000);
    }

    function checkRandomEvents() {
        apiRequest('/events/check_random_event')
        .then(data => {
            if (data.success && data.has_event) {
                showEventModal(data.event);
            }
        })
        .catch(error => {
            console.error('检查随机事件失败:', error);
        });
    }



    function showEventModal(event) {
        const modalHtml = `
            <div class="modal fade" id="eventModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header ${event.type === 'positive' ? 'bg-success' : event.type === 'negative' ? 'bg-danger' : 'bg-info'} text-white">
                            <h5 class="modal-title">
                                <i class="bi ${event.type === 'positive' ? 'bi-emoji-smile' : event.type === 'negative' ? 'bi-emoji-frown' : 'bi-info-circle'} me-2"></i>
                                ${event.name}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${event.description}</p>
                            ${event.money_effect ? `<p class="mb-1"><strong>资金影响：</strong><span class="${event.money_effect > 0 ? 'text-success' : 'text-danger'}">${event.money_effect > 0 ? '+' : ''}¥${Math.abs(event.money_effect).toLocaleString()}</span></p>` : ''}
                            ${event.reputation_effect ? `<p class="mb-1"><strong>声望影响：</strong><span class="${event.reputation_effect > 0 ? 'text-success' : 'text-danger'}">${event.reputation_effect > 0 ? '+' : ''}${event.reputation_effect}</span></p>` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="handleEvent('${event.id}', 'dismiss')">忽略</button>
                            <button type="button" class="btn btn-primary" onclick="handleEvent('${event.id}', 'accept')">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除旧的模态框
        const oldModal = document.getElementById('eventModal');
        if (oldModal) {
            oldModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('eventModal'));
        modal.show();
    }

    function handleEvent(eventId, action) {
        apiRequest('/events/handle_event', {
            method: 'POST',
            body: JSON.stringify({
                event_id: eventId,
                action: action
            })
        })
        .then(data => {
            if (data.success) {
                showMessage(data.message, action === 'accept' ? 'success' : 'info');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('eventModal'));
                if (modal) {
                    modal.hide();
                }
                // 更新酒店信息
                setTimeout(() => updateAllData(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('处理事件失败', 'error');
        });
    }

    // 更新财务汇总表
    async function updateFinancialSummary() {
        try {
            const data = await apiRequest('/api/financial_summary');

            if (data.success) {
                const tbody = document.getElementById('financialSummaryBody');
                if (!tbody) return;

                tbody.innerHTML = '';

                data.monthly_data.forEach(monthData => {
                    const row = document.createElement('tr');

                    // 当月数据高亮显示
                    if (monthData.is_current) {
                        row.classList.add('table-warning');
                    }

                    // 净利润颜色
                    const profitClass = monthData.net_profit >= 0 ? 'text-success' : 'text-danger';
                    const profitIcon = monthData.net_profit >= 0 ? '📈' : '📉';

                    row.innerHTML = `
                        <td class="text-center">
                            <strong>${monthData.month_str}</strong>
                            ${monthData.is_current ? '<br><small class="badge bg-warning text-dark">当月</small>' : ''}
                        </td>
                        <td class="text-end text-success">
                            <strong>¥${monthData.total_income.toLocaleString()}</strong>
                        </td>
                        <td class="text-end text-danger">
                            <strong>¥${monthData.total_expense.toLocaleString()}</strong>
                        </td>
                        <td class="text-end ${profitClass}">
                            <strong>¥${monthData.net_profit.toLocaleString()}</strong>
                        </td>
                        <td class="text-center">
                            <span style="font-size: 1.2em;">${profitIcon}</span>
                        </td>
                    `;

                    tbody.appendChild(row);
                });

                console.log('💰 财务汇总更新完成');
            }
        } catch (error) {
            console.error('获取财务汇总失败:', error);
            const tbody = document.getElementById('financialSummaryBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            加载失败，请刷新页面重试
                        </td>
                    </tr>
                `;
            }
        }
    }

    // 检查随机事件和月度报告
    async function checkEvents() {
        try {
            const data = await apiRequest('/api/check_events');

            if (data.success) {
                // 处理随机事件
                if (data.events && data.events.length > 0) {
                    for (const event of data.events) {
                        showRandomEvent(event);
                        await new Promise(resolve => setTimeout(resolve, 3000)); // 暂停3秒
                    }
                }

                // 处理月度报告
                if (data.monthly_report) {
                    showMonthlyReport(data.monthly_report);
                }
            }
        } catch (error) {
            console.error('检查事件失败:', error);
        }
    }

    // 显示随机事件（简化为提示消息）
    function showRandomEvent(event) {
        // 设置事件类型样式
        let messageType = 'info';
        let icon = '📢';

        if (event.event_type === 'positive') {
            messageType = 'success';
            icon = '✅';
        } else if (event.event_type === 'negative') {
            messageType = 'error';
            icon = '❌';
        } else {
            messageType = 'info';
            icon = 'ℹ️';
        }

        // 显示简单的提示消息
        showMessage(`${icon} ${event.name}: ${event.description}`, messageType, 4000);
    }

    // 显示月度报告
    function showMonthlyReport(report) {
        const modal = document.getElementById('monthlyReportModal');
        if (!modal) return;

        // 设置报告标题
        document.getElementById('reportTitle').textContent = `${report.month_str} 财务报告`;

        // 设置总览数据
        document.getElementById('reportIncome').textContent = `¥${report.total_income.toLocaleString()}`;
        document.getElementById('reportExpense').textContent = `¥${report.total_expense.toLocaleString()}`;
        document.getElementById('reportProfit').textContent = `¥${report.net_profit.toLocaleString()}`;
        document.getElementById('reportProfit').className = report.net_profit >= 0 ? 'text-success' : 'text-danger';

        // 设置收入明细
        const incomeList = document.getElementById('incomeDetails');
        incomeList.innerHTML = '';
        for (const [category, amount] of Object.entries(report.income_categories)) {
            const li = document.createElement('li');
            li.className = 'list-group-item d-flex justify-content-between';
            li.innerHTML = `<span>${category}</span><span class="text-success">¥${amount.toLocaleString()}</span>`;
            incomeList.appendChild(li);
        }

        // 设置支出明细
        const expenseList = document.getElementById('expenseDetails');
        expenseList.innerHTML = '';
        for (const [category, amount] of Object.entries(report.expense_categories)) {
            const li = document.createElement('li');
            li.className = 'list-group-item d-flex justify-content-between';
            li.innerHTML = `<span>${category}</span><span class="text-danger">¥${amount.toLocaleString()}</span>`;
            expenseList.appendChild(li);
        }

        // 显示模态框
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

