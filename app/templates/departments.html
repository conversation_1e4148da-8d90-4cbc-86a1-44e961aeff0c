{% extends "base.html" %}

{% block title %}部门管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-diagram-3-fill text-primary me-2"></i>部门管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<!-- 部门概况 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-pie-chart text-primary me-2"></i>部门概况
                </h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-check-circle-fill text-success fs-3 mb-2"></i>
                            <h4 class="text-success mb-1">{{ all_departments|selectattr('is_unlocked')|list|length }}</h4>
                            <small class="text-muted">已解锁部门</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-people-fill text-primary fs-3 mb-2"></i>
                            <h4 class="text-primary mb-1">{{ department_employee_counts.values()|sum }}</h4>
                            <small class="text-muted">员工总数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-speedometer2 text-warning fs-3 mb-2"></i>
                            {% set unlocked_busy_levels = [] %}
                            {% for dept in all_departments %}
                                {% if dept.is_unlocked %}
                                    {% set _ = unlocked_busy_levels.append(department_busy_levels.get(dept.name, 0)) %}
                                {% endif %}
                            {% endfor %}
                            <h4 class="text-warning mb-1">{{ "%.1f"|format(unlocked_busy_levels|sum / unlocked_busy_levels|length if unlocked_busy_levels|length > 0 else 0) }}%</h4>
                            <small class="text-muted">平均繁忙度</small>
                    <button class="btn btn-outline-info btn-sm ms-2" data-bs-toggle="modal" data-bs-target="#busyLevelHelpModal" title="繁忙度计算说明">
                        <i class="bi bi-question-circle"></i>
                    </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-column align-items-center p-3">
                            <i class="bi bi-lock-fill text-secondary fs-3 mb-2"></i>
                            <h4 class="text-secondary mb-1">{{ all_departments|rejectattr('is_unlocked')|list|length }}</h4>
                            <small class="text-muted">待解锁部门</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 部门列表 -->
<div class="card border-0 shadow-sm">
    <div class="card-body">
        <h5 class="card-title mb-3">
            <i class="bi bi-diagram-3-fill text-primary me-2"></i>部门管理
        </h5>
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th width="5%">状态</th>
                        <th width="15%">部门名称</th>
                        <th width="8%">总员工</th>
                        <th width="8%">初级</th>
                        <th width="8%">中级</th>
                        <th width="8%">高级</th>
                        <th width="8%">特级</th>
                        <th width="10%">繁忙度</th>
                        <th width="15%">解锁条件</th>
                        <th width="10%">解锁费用</th>
                        <th width="5%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in all_departments %}
                    <tr class="{% if department.is_unlocked %}table-success{% endif %}">
                        <td>
                            {% if department.is_unlocked %}
                            <i class="bi bi-check-circle-fill text-success fs-5"></i>
                            {% else %}
                            <i class="bi bi-lock-fill text-muted fs-5"></i>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <strong>{{ department.name }}</strong>
                                {% if department.is_unlocked %}
                                <span class="badge bg-success ms-2">已解锁</span>
                                {% else %}
                                <span class="badge bg-secondary ms-2">未解锁</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-primary">{{ department_employee_counts.get(department.name, 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-secondary">{{ department_level_counts.get(department.name, {}).get('初级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-info">{{ department_level_counts.get(department.name, {}).get('中级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-success">{{ department_level_counts.get(department.name, {}).get('高级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="badge bg-warning">{{ department_level_counts.get(department.name, {}).get('特级', 0) }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <div class="d-flex align-items-center">
                                <div class="progress me-2" style="width: 80px; height: 8px;">
                                    <div class="progress-bar bg-info" style="width: {{ department_busy_levels.get(department.name, 0) }}%"></div>
                                </div>
                                <small>{{ "%.1f"|format(department_busy_levels.get(department.name, 0)) }}%</small>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not department.is_unlocked %}
                            <small class="text-muted">需要{{ department.required_level }}星酒店</small>
                            {% else %}
                            <span class="text-success">✓ 已满足</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not department.is_unlocked %}
                            <span class="text-warning">¥{{ "{:,}".format(department.unlock_cost) }}</span>
                            {% else %}
                            <span class="text-success">已支付</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if department.is_unlocked %}
                            <span class="text-muted">--</span>
                            {% elif department.can_unlock %}
                            <button class="btn btn-sm btn-warning" onclick="unlockDepartment({{ department.id }})">
                                <i class="bi bi-unlock-fill"></i>
                            </button>
                            {% else %}
                            <span class="text-muted">--</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- 繁忙度计算说明模态框 -->
<div class="modal fade" id="busyLevelHelpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📊 部门繁忙度计算公式</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>🧮 核心计算公式</h6>
                <div class="alert alert-info">
                    <strong>部门繁忙度 = min(200%, (估算客户数 ÷ 部门总服务能力) × 100%)</strong>
                </div>

                <h6>👥 估算客户数计算</h6>
                <p><strong>估算客户数 = Σ(房间数量 × 入住率 × 每房客人数)</strong></p>
                <ul>
                    <li><strong>所有房型</strong>：1人/房（简化计算）</li>
                </ul>

                <h6>⚡ 部门服务能力计算</h6>
                <p><strong>部门总服务能力 = Σ(基础服务能力 × 员工等级系数)</strong></p>

                <div class="row">
                    <div class="col-md-6">
                        <h6>基础服务能力（每人每天）</h6>
                        <ul class="small">
                            <li>前台部：15人 <span class="text-muted">(需要更多人手)</span></li>
                            <li>客房部：10人 <span class="text-muted">(需要更多人手)</span></li>
                            <li>餐饮部：25人</li>
                            <li>营销部：40人</li>
                            <li>安保部：50人</li>
                            <li>财务部：60人 <span class="text-success">(高级部门)</span></li>
                            <li>商务部：80人 <span class="text-success">(高级部门)</span></li>
                            <li>康养部：70人 <span class="text-success">(高级部门)</span></li>
                            <li>工程部：100人 <span class="text-success">(高级部门)</span></li>
                            <li>人事部：50人</li>
                            <li>董事会：150人 <span class="text-warning">(最高级)</span></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>员工等级系数</h6>
                        <ul class="small">
                            <li>初级员工：1.0倍</li>
                            <li>中级员工：1.5倍</li>
                            <li>高级员工：2.0倍</li>
                            <li>特级员工：3.0倍</li>
                        </ul>
                    </div>
                </div>

                <h6>📈 繁忙度对满意度的影响</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-success">
                            <strong>&lt; 50%</strong>：满意度 +10分<br>
                            <small>员工轻松，服务质量高</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <strong>50-80%</strong>：满意度 +0分<br>
                            <small>正常工作负荷</small>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <strong>80-100%</strong>：满意度 -5分<br>
                            <small>工作负荷较重</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-danger">
                            <strong>&gt; 100%</strong>：满意度 -15分<br>
                            <small>员工过度繁忙，服务质量下降</small>
                        </div>
                    </div>
                </div>

                <h6>💡 优化建议</h6>
                <ul>
                    <li><strong>繁忙度过高</strong>：招聘更多员工或升级现有员工</li>
                    <li><strong>繁忙度过低</strong>：可以适当减少员工或扩大房间规模</li>
                    <li><strong>理想范围</strong>：保持在50-80%之间获得最佳效果</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function unlockDepartment(departmentId) {
    if (confirm('确定要解锁这个部门吗？')) {
        apiRequest(`/departments/unlock/${departmentId}`, {
            method: 'POST'
        }).then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        }).catch(error => {
            showMessage('解锁部门失败', 'error');
        });
    }
}
</script>
{% endblock %}
