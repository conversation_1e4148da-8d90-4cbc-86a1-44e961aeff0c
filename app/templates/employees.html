{% extends "base.html" %}

{% block title %}员工管理 - {{ hotel.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="bi bi-people-fill text-success me-2"></i>员工管理</h2>
            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-house-fill me-1"></i>返回首页
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">员工概况</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ total_employees }}</h4>
                        <small class="text-muted">员工总数</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">¥{{ "{:,}".format(total_salary) }}</h4>
                        <small class="text-muted">月薪总额</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">{{ "%.1f"|format(avg_level) }}</h4>
                        <small class="text-muted">平均等级</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ "%.1f"|format(avg_satisfaction) }}%</h4>
                        <small class="text-muted">平均满意度</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">员工列表</h5>
                <button class="btn btn-primary btn-sm" onclick="showHireModal()">
                    <i class="bi bi-person-plus-fill me-1"></i>招聘员工
                </button>
            </div>
            <div class="card-body">
                <!-- 筛选条件 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="filterDepartment" class="form-label">按部门筛选</label>
                        <select class="form-select" id="filterDepartment" onchange="applyFilters()">
                            <option value="">全部部门</option>
                            {% for dept in departments %}
                            {% if dept.is_unlocked %}
                            <option value="{{ dept.name }}" {% if filter_department == dept.name %}selected{% endif %}>
                                {{ dept.name }}
                            </option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="filterLevel" class="form-label">按等级筛选</label>
                        <select class="form-select" id="filterLevel" onchange="applyFilters()">
                            <option value="">全部等级</option>
                            <option value="初级" {% if filter_level == '初级' %}selected{% endif %}>初级</option>
                            <option value="中级" {% if filter_level == '中级' %}selected{% endif %}>中级</option>
                            <option value="高级" {% if filter_level == '高级' %}selected{% endif %}>高级</option>
                            <option value="特级" {% if filter_level == '特级' %}selected{% endif %}>特级</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end justify-content-between">
                        <button class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="bi bi-x-circle me-1"></i>清除筛选
                        </button>
                        <small class="text-muted" id="filterCount">显示 {{ employees|length }} / {{ employees|length }} 名员工</small>
                    </div>
                </div>

                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>部门</th>
                                <th>等级</th>
                                <th>实际工资</th>
                                <th>工龄</th>
                                <th>入职日期</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="employeeTableBody">
                            {% for employee in employees %}
                            <tr data-department="{{ employee.department }}" data-level="{{ employee.level }}">
                                <td>{{ employee.name }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ employee.department }}</span>
                                </td>
                                <td>{{ employee.level }}</td>
                                <td>¥{{ "{:,}".format(employee.actual_salary) }}</td>
                                <td>{{ employee.work_age }}年</td>
                                <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '未知' }}</td>
                                <td>
                                    <button class="btn btn-outline-danger btn-sm" onclick="fireEmployee({{ employee.id }})" title="解雇员工">
                                        <i class="bi bi-person-dash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="员工列表分页">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('employees.management', page=pagination.prev_num, department=filter_department, level=filter_level) }}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('employees.management', page=page_num, department=filter_department, level=filter_level) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('employees.management', page=pagination.next_num, department=filter_department, level=filter_level) }}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>

                    <div class="text-center text-muted small">
                        显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page <= pagination.total else pagination.total }} 条，共 {{ pagination.total }} 条记录
                    </div>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无员工</h4>
                    <p class="text-muted">点击"招聘员工"按钮开始招聘</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 招聘员工模态框 -->
<div class="modal fade" id="hireModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus-fill me-2"></i>招聘员工
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="hireForm">
                    <div class="mb-3">
                        <label for="hireDepartment" class="form-label">选择部门</label>
                        <select class="form-select" id="hireDepartment" required>
                            <option value="">请选择部门</option>
                            {% for dept in departments %}
                            {% if dept.is_unlocked %}
                            <option value="{{ dept.name }}">{{ dept.name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="hireLevel" class="form-label">选择等级</label>
                        <select class="form-select" id="hireLevel" required>
                            <option value="">请选择等级</option>
                            <option value="初级">初级 - 基础工资¥3,000/月 - 招聘费¥10,000</option>
                            <option value="中级">中级 - 基础工资¥5,000/月 - 招聘费¥30,000</option>
                            <option value="高级">高级 - 基础工资¥8,000/月 - 招聘费¥80,000</option>
                            <option value="特级">特级 - 基础工资¥15,000/月 - 招聘费¥200,000</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="hireQuantity" class="form-label">招聘数量</label>
                        <select class="form-select" id="hireQuantity" required>
                            <option value="1">1人</option>
                            <option value="5">5人</option>
                            <option value="10">10人</option>
                            <option value="20">20人</option>
                            <option value="50">50人</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>招聘说明：</strong><br>
                        • 招聘费用将从酒店资金中扣除<br>
                        • 员工将立即加入选定部门<br>
                        • 实际工资 = 基础工资 + 工龄加成（每年+10%）<br>
                        • 需要对应部门已解锁
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="hireEmployee()">确认招聘</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showHireModal() {
    const modal = new bootstrap.Modal(document.getElementById('hireModal'));
    modal.show();
}

function hireEmployee() {
    const department = document.getElementById('hireDepartment').value;
    const level = document.getElementById('hireLevel').value;
    const quantity = parseInt(document.getElementById('hireQuantity').value) || 1;

    if (!department || !level) {
        showMessage('请选择部门和等级', 'error');
        return;
    }

    const levelCosts = {"初级": 10000, "中级": 30000, "高级": 80000, "特级": 200000};
    const cost = levelCosts[level];
    const totalCost = cost * quantity;

    const quantityText = quantity === 1 ? '一名' : `${quantity}名`;
    if (confirm(`确定要招聘${quantityText}${level}员工到${department}部门吗？\n总招聘费用：¥${totalCost.toLocaleString()}`)) {
        // 禁用按钮防重复点击
        const button = document.querySelector('#hireModal .btn-primary');
        if (button) disableButton(button, 3000);

        apiRequest('/employees/hire_multiple', {
            method: 'POST',
            body: JSON.stringify({
                department: department,
                level: level,
                quantity: quantity
            })
        })
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('hireModal'));
                modal.hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('招聘失败', 'error');
        });
    }
}

// 员工解雇功能
function fireEmployee(employeeId) {
    // 先获取解雇费用
    apiRequest('/employees/get_fire_cost', {
        method: 'POST',
        body: JSON.stringify({employee_id: employeeId})
    })
    .then(data => {
        if (data.success) {
            const fireCost = data.fire_cost;
            const employeeName = data.employee_name;
            if (confirm(`确定要解雇员工 ${employeeName} 吗？\n需支付解雇补偿金：¥${fireCost.toLocaleString()}`)) {
                apiRequest('/employees/fire', {
                    method: 'POST',
                    body: JSON.stringify({employee_id: employeeId})
                })
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('解雇失败', 'error');
                });
            }
        } else {
            showMessage('获取解雇费用失败', 'error');
        }
    })
    .catch(error => {
        showMessage('获取解雇费用失败', 'error');
    });
}

function viewEmployee(employeeId) {
    showMessage('员工详情功能开发中...', 'info');
}

// 晋升功能已移除 - 员工晋升每年自动进行

// 筛选功能 - 优化为即时筛选，避免页面重新加载
function applyFilters() {
    const department = document.getElementById('filterDepartment').value;
    const level = document.getElementById('filterLevel').value;

    // 获取所有员工行
    const employeeRows = document.querySelectorAll('#employeeTableBody tr');
    let visibleCount = 0;

    employeeRows.forEach(row => {
        const rowDepartment = row.getAttribute('data-department');
        const rowLevel = row.getAttribute('data-level');

        let shouldShow = true;

        // 部门筛选
        if (department && rowDepartment !== department) {
            shouldShow = false;
        }

        // 等级筛选
        if (level && rowLevel !== level) {
            shouldShow = false;
        }

        if (shouldShow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // 更新显示计数
    updateFilterCount(visibleCount);
}

function clearFilters() {
    document.getElementById('filterDepartment').value = '';
    document.getElementById('filterLevel').value = '';

    // 显示所有行
    const employeeRows = document.querySelectorAll('#employeeTableBody tr');
    employeeRows.forEach(row => {
        row.style.display = '';
    });

    // 更新显示计数
    updateFilterCount(employeeRows.length);
}

function updateFilterCount(count) {
    const totalCount = document.querySelectorAll('#employeeTableBody tr').length;
    const countElement = document.getElementById('filterCount');
    if (countElement) {
        countElement.textContent = `显示 ${count} / ${totalCount} 名员工`;
    }
}
</script>
{% endblock %}
