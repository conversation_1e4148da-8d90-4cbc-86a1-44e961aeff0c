#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
存档系统视图
"""

from flask import Blueprint, jsonify, request, render_template
from app.models import (Hotel, Employee, Department, Room, MarketingCampaign, FinancialRecord,
                        Achievement, SeasonalEffect, RandomEvent, GameSetting, OccupancyRecord, db)
from app.main.utils import get_current_hotel
import json
import logging
from datetime import datetime, date
import os

logger = logging.getLogger(__name__)

bp = Blueprint('save', __name__, url_prefix='/save')

@bp.route('/create', methods=['POST'])
def create_save():
    """创建存档"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        save_name = data.get('save_name', f"存档_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

        # 收集所有游戏数据
        save_data = {
            'save_name': save_name,
            'created_at': datetime.now().isoformat(),
            'hotel': {
                'name': hotel.name,
                'level': hotel.level,
                'money': hotel.money,
                'reputation': hotel.reputation,
                'date': hotel.date.isoformat() if hotel.date else None,
                'start_date': hotel.start_date.isoformat() if hotel.start_date else None
            },
            'employees': [],
            'departments': [],
            'rooms': [],
            'marketing_campaigns': [],
            'financial_records': [],
            'achievements': []
        }

        # 收集员工数据
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()
        for emp in employees:
            save_data['employees'].append({
                'name': emp.name,
                'department': emp.department,
                'level': emp.level,
                'salary': emp.salary,
                'years_worked': emp.years_worked,
                'hire_date': emp.hire_date.isoformat() if emp.hire_date else None
            })

        # 收集部门数据
        departments = Department.query.filter_by(hotel_id=hotel.id).all()
        for dept in departments:
            save_data['departments'].append({
                'name': dept.name,
                'is_unlocked': dept.is_unlocked
            })

        # 收集房间数据
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        for room in rooms:
            save_data['rooms'].append({
                'type': room.type,
                'count': room.count
            })

        # 收集营销活动数据
        campaigns = MarketingCampaign.query.filter_by(hotel_id=hotel.id).all()
        for campaign in campaigns:
            save_data['marketing_campaigns'].append({
                'campaign_id': campaign.campaign_id,
                'is_active': campaign.is_active,
                'start_date': campaign.start_date.isoformat() if campaign.start_date else None,
                'end_date': campaign.end_date.isoformat() if campaign.end_date else None
            })

        # 收集财务记录（最近100条）
        records = FinancialRecord.query.filter_by(hotel_id=hotel.id).order_by(FinancialRecord.record_date.desc()).limit(100).all()
        for record in records:
            save_data['financial_records'].append({
                'record_date': record.record_date.isoformat() if record.record_date else None,
                'description': record.description,
                'income': record.income,
                'expense': record.expense
            })

        # 收集成就数据
        achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()
        for achievement in achievements:
            save_data['achievements'].append({
                'name': achievement.name,
                'description': achievement.description,
                'category': achievement.category,
                'achieved': achievement.achieved,
                'achieved_date': achievement.achieved_date.isoformat() if achievement.achieved_date else None,
                'reward_claimed': getattr(achievement, 'reward_claimed', False),
                'reward_money': getattr(achievement, 'reward_money', 0),
                'reward_reputation': achievement.reward_reputation
            })

        # 保存到文件
        save_dir = 'saves'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        save_file = os.path.join(save_dir, f"{save_name}.json")
        with open(save_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        return jsonify({
            "success": True,
            "message": f"存档'{save_name}'创建成功",
            "save_file": save_file
        })

    except Exception as e:
        logger.error(f"创建存档时出错: {e}")
        return jsonify({"success": False, "message": "创建存档失败"}), 500

@bp.route('/list', methods=['GET'])
def list_saves():
    """获取存档列表"""
    try:
        save_dir = 'saves'
        if not os.path.exists(save_dir):
            return jsonify({"success": True, "saves": []})

        saves = []
        for filename in os.listdir(save_dir):
            if filename.endswith('.json'):
                save_file = os.path.join(save_dir, filename)
                try:
                    with open(save_file, 'r', encoding='utf-8') as f:
                        save_data = json.load(f)
                    
                    saves.append({
                        'filename': filename,
                        'save_name': save_data.get('save_name', filename[:-5]),
                        'created_at': save_data.get('created_at'),
                        'hotel_name': save_data.get('hotel', {}).get('name', '未知'),
                        'hotel_level': save_data.get('hotel', {}).get('level', 1),
                        'hotel_money': save_data.get('hotel', {}).get('money', 0)
                    })
                except:
                    continue

        saves.sort(key=lambda x: x['created_at'], reverse=True)
        return jsonify({"success": True, "saves": saves})

    except Exception as e:
        logger.error(f"获取存档列表时出错: {e}")
        return jsonify({"success": False, "message": "获取存档列表失败"}), 500

@bp.route('/load', methods=['POST'])
def load_save():
    """读取存档"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        
        if not filename:
            return jsonify({"success": False, "message": "存档文件名不能为空"}), 400

        save_file = os.path.join('saves', filename)
        if not os.path.exists(save_file):
            return jsonify({"success": False, "message": "存档文件不存在"}), 404

        # 读取存档数据
        with open(save_file, 'r', encoding='utf-8') as f:
            save_data = json.load(f)

        # 清空当前数据
        hotel = get_current_hotel()
        if hotel:
            # 删除相关数据
            Employee.query.filter_by(hotel_id=hotel.id).delete()
            Department.query.filter_by(hotel_id=hotel.id).delete()
            Room.query.filter_by(hotel_id=hotel.id).delete()
            MarketingCampaign.query.filter_by(hotel_id=hotel.id).delete()
            FinancialRecord.query.filter_by(hotel_id=hotel.id).delete()
            Achievement.query.filter_by(hotel_id=hotel.id).delete()
            
            # 更新酒店数据
            hotel_data = save_data['hotel']
            hotel.name = hotel_data['name']
            hotel.level = hotel_data['level']
            hotel.money = hotel_data['money']
            hotel.reputation = hotel_data['reputation']
            hotel.date = datetime.fromisoformat(hotel_data['date']).date() if hotel_data['date'] else date.today()
            hotel.start_date = datetime.fromisoformat(hotel_data['start_date']).date() if hotel_data['start_date'] else date.today()

        else:
            # 创建新酒店
            hotel_data = save_data['hotel']
            hotel = Hotel(
                name=hotel_data['name'],
                level=hotel_data['level'],
                money=hotel_data['money'],
                reputation=hotel_data['reputation'],
                date=datetime.fromisoformat(hotel_data['date']).date() if hotel_data['date'] else date.today(),
                start_date=datetime.fromisoformat(hotel_data['start_date']).date() if hotel_data['start_date'] else date.today()
            )
            db.session.add(hotel)
            db.session.flush()  # 获取hotel.id

        # 恢复员工数据
        for emp_data in save_data['employees']:
            employee = Employee(
                hotel_id=hotel.id,
                name=emp_data['name'],
                department=emp_data['department'],
                level=emp_data['level'],
                salary=emp_data['salary'],
                years_worked=emp_data['years_worked'],
                hire_date=datetime.fromisoformat(emp_data['hire_date']).date() if emp_data['hire_date'] else date.today()
            )
            db.session.add(employee)

        # 恢复部门数据
        for dept_data in save_data['departments']:
            department = Department(
                hotel_id=hotel.id,
                name=dept_data['name'],
                is_unlocked=dept_data['is_unlocked']
            )
            db.session.add(department)

        # 恢复房间数据
        for room_data in save_data['rooms']:
            room = Room(
                hotel_id=hotel.id,
                type=room_data['type'],
                count=room_data['count']
            )
            db.session.add(room)

        # 恢复营销活动数据
        for campaign_data in save_data['marketing_campaigns']:
            campaign = MarketingCampaign(
                hotel_id=hotel.id,
                campaign_id=campaign_data['campaign_id'],
                is_active=campaign_data['is_active'],
                start_date=datetime.fromisoformat(campaign_data['start_date']).date() if campaign_data['start_date'] else None,
                end_date=datetime.fromisoformat(campaign_data['end_date']).date() if campaign_data['end_date'] else None
            )
            db.session.add(campaign)

        # 恢复财务记录
        for record_data in save_data['financial_records']:
            record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=datetime.fromisoformat(record_data['record_date']).date() if record_data['record_date'] else date.today(),
                description=record_data['description'],
                income=record_data['income'],
                expense=record_data['expense']
            )
            db.session.add(record)

        # 恢复成就数据
        for achievement_data in save_data['achievements']:
            achievement = Achievement(
                hotel_id=hotel.id,
                name=achievement_data['name'],
                description=achievement_data['description'],
                category=achievement_data['category'],
                condition_type='custom',
                condition_value=0,
                reward_reputation=achievement_data['reward_reputation'],
                reward_money=achievement_data.get('reward_money', 0),
                achieved=achievement_data['achieved'],
                achieved_date=datetime.fromisoformat(achievement_data['achieved_date']).date() if achievement_data['achieved_date'] else None,
                reward_claimed=achievement_data.get('reward_claimed', False)
            )
            db.session.add(achievement)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"存档'{save_data['save_name']}'读取成功"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"读取存档时出错: {e}")
        return jsonify({"success": False, "message": "读取存档失败"}), 500

@bp.route('/restart', methods=['POST'])
def restart_game():
    """重新开始游戏"""
    try:
        # 停止时间线程
        from app.main.utils import stop_time_thread, start_time_thread
        stop_time_thread()

        # 清空所有数据
        hotel = get_current_hotel()
        if hotel:
            # 清空所有与酒店ID关联的数据
            Employee.query.filter_by(hotel_id=hotel.id).delete()
            Department.query.filter_by(hotel_id=hotel.id).delete()
            Room.query.filter_by(hotel_id=hotel.id).delete()
            MarketingCampaign.query.filter_by(hotel_id=hotel.id).delete()
            FinancialRecord.query.filter_by(hotel_id=hotel.id).delete()
            Achievement.query.filter_by(hotel_id=hotel.id).delete()
            SeasonalEffect.query.filter_by(hotel_id=hotel.id).delete()
            RandomEvent.query.filter_by(hotel_id=hotel.id).delete()
            GameSetting.query.filter_by(hotel_id=hotel.id).delete()
            OccupancyRecord.query.filter_by(hotel_id=hotel.id).delete()

            # 提交删除操作
            db.session.commit()

            # 重置酒店数据到初始状态
            hotel.name = "红珊瑚大酒店"
            hotel.level = 1
            hotel.money = 1000000  # 100万初始资金
            hotel.reputation = 0   # 初始声望为0
            hotel.reputation_level = 1  # 声望等级为1
            hotel.satisfaction = 50.0  # 初始满意度50
            hotel.date = date(1990, 1, 1)  # 重置为游戏开始时间
            hotel.start_date = date(1990, 1, 1)
            hotel.days_elapsed = 0  # 重置经营天数
            hotel.time_running = False  # 暂时停止时间推进
            hotel.time_speed = 1
        else:
            # 创建新酒店
            hotel = Hotel(
                name="红珊瑚大酒店",
                level=1,
                money=1000000,
                reputation=0,
                reputation_level=1,
                satisfaction=50.0,
                date=date(1990, 1, 1),
                start_date=date(1990, 1, 1),
                days_elapsed=0,
                time_running=False,  # 暂时停止时间推进
                time_speed=1
            )
            db.session.add(hotel)
            db.session.flush()

        # 创建基础房间（单人间和标准间各10个）
        room_types = ["单人间", "标准间"]
        room_prices = {"单人间": 300, "标准间": 500}

        for room_type in room_types:
            room = Room(hotel_id=hotel.id, type=room_type, count=10, price=room_prices.get(room_type, 300))
            db.session.add(room)

        # 创建所有部门
        departments = [
            {"name": "前台部", "is_unlocked": True, "unlock_cost": 0},
            {"name": "客房部", "is_unlocked": True, "unlock_cost": 0},
            {"name": "人事部", "is_unlocked": True, "unlock_cost": 0},  # 人事部默认解锁
            {"name": "营销部", "is_unlocked": False, "unlock_cost": 500000},
            {"name": "餐饮部", "is_unlocked": False, "unlock_cost": 1000000},
            {"name": "安保部", "is_unlocked": False, "unlock_cost": 2000000},
            {"name": "财务部", "is_unlocked": False, "unlock_cost": 5000000},
            {"name": "商务部", "is_unlocked": False, "unlock_cost": 10000000},
            {"name": "工程部", "is_unlocked": False, "unlock_cost": 30000000},
            {"name": "康养部", "is_unlocked": False, "unlock_cost": 60000000},
            {"name": "董事会", "is_unlocked": False, "unlock_cost": 100000000}
        ]

        for dept_data in departments:
            department = Department(
                hotel_id=hotel.id,
                name=dept_data["name"],
                is_unlocked=dept_data["is_unlocked"],
                unlock_cost=dept_data["unlock_cost"]
            )
            db.session.add(department)

        # 初始化成就系统
        achievements_data = [
            # 财务成就 (10个)
            {'name': '初次盈利', 'description': '酒店资金超过初始资金', 'category': '财务成就'},
            {'name': '百万富翁', 'description': '酒店资金达到100万元', 'category': '财务成就'},
            {'name': '千万富翁', 'description': '酒店资金达到1000万元', 'category': '财务成就'},
            {'name': '亿万富翁', 'description': '酒店资金达到1亿元', 'category': '财务成就'},
            {'name': '月入百万', 'description': '单月收入达到100万元', 'category': '财务成就'},
            {'name': '月入千万', 'description': '单月收入达到1000万元', 'category': '财务成就'},
            {'name': '年度盈利王', 'description': '年度总盈利达到1亿元', 'category': '财务成就'},
            {'name': '连续盈利', 'description': '连续30天盈利', 'category': '财务成就'},
            {'name': '财务大师', 'description': '累计盈利达到10亿元', 'category': '财务成就'},
            {'name': '投资专家', 'description': '单日盈利达到1000万元', 'category': '财务成就'},

            # 员工成就 (10个)
            {'name': '首位员工', 'description': '招聘第一名员工', 'category': '员工成就'},
            {'name': '十人团队', 'description': '拥有10名员工', 'category': '员工成就'},
            {'name': '百人团队', 'description': '拥有100名员工', 'category': '员工成就'},
            {'name': '千人企业', 'description': '拥有1000名员工', 'category': '员工成就'},
            {'name': '人才济济', 'description': '拥有50名高级员工', 'category': '员工成就'},
            {'name': '精英团队', 'description': '拥有10名特级员工', 'category': '员工成就'},
            {'name': '全能发展', 'description': '所有部门都有员工', 'category': '员工成就'},
            {'name': '薪资大户', 'description': '月薪支出达到100万元', 'category': '员工成就'},
            {'name': '员工满意', 'description': '员工平均工龄达到5年', 'category': '员工成就'},
            {'name': '人力资源专家', 'description': '成功培养100名员工升级', 'category': '员工成就'},

            # 发展成就 (10个)
            {'name': '星光初现', 'description': '酒店达到2星', 'category': '发展成就'},
            {'name': '三星酒店', 'description': '酒店达到3星', 'category': '发展成就'},
            {'name': '四星酒店', 'description': '酒店达到4星', 'category': '发展成就'},
            {'name': '五星酒店', 'description': '酒店达到5星', 'category': '发展成就'},
            {'name': '超五星酒店', 'description': '酒店达到6星', 'category': '发展成就'},
            {'name': '豪华酒店', 'description': '酒店达到7星', 'category': '发展成就'},
            {'name': '顶级酒店', 'description': '酒店达到8星', 'category': '发展成就'},
            {'name': '传奇酒店', 'description': '酒店达到9星', 'category': '发展成就'},
            {'name': '完美酒店', 'description': '酒店达到10星', 'category': '发展成就'},
            {'name': '部门大亨', 'description': '解锁所有部门', 'category': '发展成就'},

            # 经营成就 (10个)
            {'name': '满意服务', 'description': '客户满意度达到90分以上', 'category': '经营成就'},
            {'name': '声望卓著', 'description': '声望值达到10000', 'category': '经营成就'},
            {'name': '房间帝国', 'description': '拥有1000间房间', 'category': '经营成就'},
            {'name': '入住率之王', 'description': '平均入住率达到95%以上', 'category': '经营成就'},
            {'name': '营销大师', 'description': '同时进行5个营销活动', 'category': '经营成就'},
            {'name': '客户至上', 'description': '客户满意度连续30天超过85分', 'category': '经营成就'},
            {'name': '品牌价值', 'description': '声望值达到100000', 'category': '经营成就'},
            {'name': '服务标杆', 'description': '客户满意度达到95分以上', 'category': '经营成就'},
            {'name': '市场领导者', 'description': '声望等级达到最高级', 'category': '经营成就'},
            {'name': '完美经营', 'description': '同时达到满意度90分和声望50000', 'category': '经营成就'},

            # 特殊成就 (10个)
            {'name': '长期经营', 'description': '运营超过10年(3650天)', 'category': '特殊成就'},
            {'name': '快速发展', 'description': '1年内达到5星', 'category': '特殊成就'},
            {'name': '百年老店', 'description': '运营超过100年', 'category': '特殊成就'},
            {'name': '时间大师', 'description': '使用10倍速度运营', 'category': '特殊成就'},
            {'name': '危机处理', 'description': '成功处理10次随机事件', 'category': '特殊成就'},
            {'name': '节日专家', 'description': '成功举办50次节日活动', 'category': '特殊成就'},
            {'name': '扩张专家', 'description': '单次购买100间房间', 'category': '特殊成就'},
            {'name': '投资回报', 'description': '单日收入超过支出10倍', 'category': '特殊成就'},
            {'name': '完美开局', 'description': '开业第一年盈利超过1000万', 'category': '特殊成就'},
            {'name': '传奇经理', 'description': '获得所有其他成就', 'category': '特殊成就'}
        ]

        # 创建成就记录
        for achievement_data in achievements_data:
            # 根据成就类型设置不同的奖励
            reward_money = 0
            reward_reputation = 100

            if achievement_data['category'] == '财务成就':
                reward_money = 50000  # 财务成就给金钱奖励
                reward_reputation = 200
            elif achievement_data['category'] == '员工成就':
                reward_money = 20000
                reward_reputation = 150
            elif achievement_data['category'] == '发展成就':
                reward_money = 100000  # 发展成就给更多金钱
                reward_reputation = 300
            elif achievement_data['category'] == '经营成就':
                reward_money = 30000
                reward_reputation = 250
            elif achievement_data['category'] == '特殊成就':
                reward_money = 200000  # 特殊成就给最多奖励
                reward_reputation = 500

            achievement = Achievement(
                hotel_id=hotel.id,
                name=achievement_data['name'],
                description=achievement_data['description'],
                category=achievement_data['category'],
                condition_type='custom',
                condition_value=0,
                reward_reputation=reward_reputation,
                reward_money=reward_money,
                achieved=False,
                reward_claimed=False
            )
            db.session.add(achievement)

        # 提交所有初始化数据
        db.session.commit()

        # 重新启用时间推进
        hotel.time_running = True
        db.session.commit()

        # 重新启动时间线程
        start_time_thread()

        return jsonify({
            "success": True,
            "message": "游戏重新开始成功，所有数据已重置到初始状态"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"重新开始游戏时出错: {e}")
        return jsonify({"success": False, "message": "重新开始游戏失败"}), 500
