#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库锁定问题的终极解决方案
"""

import time
import random
import logging
from sqlalchemy.exc import OperationalError
import sqlite3
from app import db

logger = logging.getLogger(__name__)

def safe_commit_with_retry(max_retries=10, base_delay=0.05):
    """
    安全的数据库提交，带重试机制
    
    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
    
    Returns:
        bool: 提交是否成功
    """
    for attempt in range(max_retries):
        try:
            db.session.commit()
            return True
        except (OperationalError, sqlite3.OperationalError) as e:
            if "database is locked" in str(e).lower() or "locked" in str(e).lower():
                if attempt < max_retries - 1:
                    # 指数退避 + 随机抖动
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.02)
                    logger.warning(f"数据库锁定，第{attempt + 1}次重试，等待{delay:.3f}秒")
                    db.session.rollback()
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"数据库提交失败，已重试{max_retries}次")
                    db.session.rollback()
                    return False
            else:
                logger.error(f"数据库提交出错: {e}")
                db.session.rollback()
                return False
        except Exception as e:
            logger.error(f"提交操作出错: {e}")
            db.session.rollback()
            return False
    
    return False

def safe_query_with_retry(query_func, max_retries=5, base_delay=0.02):
    """
    安全的数据库查询，带重试机制
    
    Args:
        query_func: 查询函数
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
    
    Returns:
        查询结果或None
    """
    for attempt in range(max_retries):
        try:
            return query_func()
        except (OperationalError, sqlite3.OperationalError) as e:
            if "database is locked" in str(e).lower() or "locked" in str(e).lower():
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.01)
                    logger.warning(f"查询时数据库锁定，第{attempt + 1}次重试，等待{delay:.3f}秒")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"数据库查询失败，已重试{max_retries}次")
                    return None
            else:
                logger.error(f"数据库查询出错: {e}")
                return None
        except Exception as e:
            logger.error(f"查询操作出错: {e}")
            return None
    
    return None

def configure_sqlite_for_concurrency():
    """
    配置SQLite以更好地处理并发访问
    """
    try:
        # 设置WAL模式和其他优化参数
        db.session.execute("PRAGMA journal_mode=WAL")
        db.session.execute("PRAGMA synchronous=NORMAL")
        db.session.execute("PRAGMA cache_size=10000")
        db.session.execute("PRAGMA temp_store=memory")
        db.session.execute("PRAGMA mmap_size=268435456")  # 256MB
        db.session.execute("PRAGMA busy_timeout=30000")  # 30秒超时
        db.session.commit()
        logger.info("SQLite并发优化配置完成")
        return True
    except Exception as e:
        logger.error(f"SQLite配置失败: {e}")
        db.session.rollback()
        return False

def execute_with_transaction_retry(operations, max_retries=5):
    """
    在事务中执行多个操作，带重试机制
    
    Args:
        operations: 操作函数列表
        max_retries: 最大重试次数
    
    Returns:
        bool: 是否成功
    """
    for attempt in range(max_retries):
        try:
            # 开始事务
            for operation in operations:
                operation()
            
            # 提交事务
            if safe_commit_with_retry():
                return True
            else:
                if attempt < max_retries - 1:
                    logger.warning(f"事务提交失败，第{attempt + 1}次重试")
                    time.sleep(0.1 * (attempt + 1))
                    continue
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"事务执行出错: {e}")
            db.session.rollback()
            if attempt < max_retries - 1:
                time.sleep(0.1 * (attempt + 1))
                continue
            else:
                return False
    
    return False

class DatabaseLockManager:
    """数据库锁管理器"""
    
    def __init__(self):
        self.lock_count = 0
        self.max_lock_count = 100
        
    def acquire_lock(self):
        """获取锁"""
        self.lock_count += 1
        if self.lock_count > self.max_lock_count:
            logger.warning("数据库锁计数过高，可能存在死锁")
            self.lock_count = 0
            
    def release_lock(self):
        """释放锁"""
        self.lock_count = max(0, self.lock_count - 1)
        
    def is_locked(self):
        """检查是否锁定"""
        return self.lock_count > 10

# 全局锁管理器
lock_manager = DatabaseLockManager()

def smart_database_operation(operation, operation_name="数据库操作"):
    """
    智能数据库操作，自动处理锁定问题
    
    Args:
        operation: 要执行的操作函数
        operation_name: 操作名称（用于日志）
    
    Returns:
        操作结果
    """
    if lock_manager.is_locked():
        logger.warning(f"数据库繁忙，延迟执行{operation_name}")
        time.sleep(random.uniform(0.1, 0.3))
    
    lock_manager.acquire_lock()
    
    try:
        result = safe_query_with_retry(operation)
        return result
    finally:
        lock_manager.release_lock()

def batch_database_operations(operations, batch_size=10):
    """
    批量执行数据库操作，减少锁定冲突
    
    Args:
        operations: 操作列表
        batch_size: 批次大小
    
    Returns:
        bool: 是否全部成功
    """
    success_count = 0
    total_count = len(operations)
    
    for i in range(0, total_count, batch_size):
        batch = operations[i:i + batch_size]
        
        if execute_with_transaction_retry(batch):
            success_count += len(batch)
        else:
            logger.error(f"批次 {i//batch_size + 1} 执行失败")
            
        # 批次间短暂延迟
        if i + batch_size < total_count:
            time.sleep(0.01)
    
    logger.info(f"批量操作完成: {success_count}/{total_count}")
    return success_count == total_count
