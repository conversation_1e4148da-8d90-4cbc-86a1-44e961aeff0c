#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import logging
from datetime import datetime, timedelta
from app import db
from app.models import RandomEvent, Hotel, FinancialRecord

logger = logging.getLogger(__name__)

# 随机事件配置
RANDOM_EVENTS = {
    'positive': [
        {
            'name': '旅游旺季',
            'description': '当地进入旅游旺季，酒店入住率大幅提升！',
            'effect_type': 'occupancy_boost',
            'effect_value': 20,  # 入住率+20%
            'duration': 7,
            'probability': 0.3
        },
        {
            'name': '好评如潮',
            'description': '酒店获得知名旅游博主推荐，声望大幅提升！',
            'effect_type': 'reputation',
            'effect_value': 500,
            'duration': 1,
            'probability': 0.2
        },
        {
            'name': '员工表彰',
            'description': '员工获得行业表彰，团队士气高涨！',
            'effect_type': 'satisfaction',
            'effect_value': 10,
            'duration': 30,
            'probability': 0.2
        },
        {
            'name': '意外收入',
            'description': '酒店获得政府旅游业补贴！',
            'effect_type': 'money',
            'effect_value': 100000,
            'duration': 1,
            'probability': 0.15
        },
        {
            'name': '会议预订',
            'description': '大型企业预订酒店举办会议，带来额外收入！',
            'effect_type': 'money',
            'effect_value': 200000,
            'duration': 1,
            'probability': 0.15
        }
    ],
    'negative': [
        {
            'name': '设备故障',
            'description': '酒店主要设备发生故障，需要紧急维修！',
            'effect_type': 'money',
            'effect_value': -150000,
            'duration': 1,
            'probability': 0.25
        },
        {
            'name': '客户投诉',
            'description': '客户对服务不满，在网上发布负面评价！',
            'effect_type': 'satisfaction',
            'effect_value': -15,
            'duration': 1,
            'probability': 0.2
        },
        {
            'name': '员工离职',
            'description': '关键员工突然离职，影响酒店运营！',
            'effect_type': 'employee_quit',
            'effect_value': 2,  # 2名员工离职
            'duration': 1,
            'probability': 0.2
        },
        {
            'name': '恶劣天气',
            'description': '恶劣天气影响客流，入住率下降！',
            'effect_type': 'occupancy_drop',
            'effect_value': -15,  # 入住率-15%
            'duration': 3,
            'probability': 0.2
        },
        {
            'name': '竞争对手',
            'description': '附近开设新酒店，分流了部分客户！',
            'effect_type': 'reputation',
            'effect_value': -200,
            'duration': 1,
            'probability': 0.15
        }
    ],
    'neutral': [
        {
            'name': '政策调整',
            'description': '政府调整旅游业政策，对酒店影响中性。',
            'effect_type': 'none',
            'effect_value': 0,
            'duration': 1,
            'probability': 0.3
        },
        {
            'name': '行业会议',
            'description': '参加酒店行业会议，了解最新趋势。',
            'effect_type': 'none',
            'effect_value': 0,
            'duration': 1,
            'probability': 0.2
        }
    ]
}

def trigger_random_event(hotel):
    """触发随机事件（确保每月至少2个事件）"""
    try:
        # 检查本月已触发的事件数量
        from datetime import datetime
        current_month_start = hotel.date.replace(day=1)

        monthly_events = RandomEvent.query.filter(
            RandomEvent.hotel_id == hotel.id,
            RandomEvent.triggered_at >= datetime.combine(current_month_start, datetime.min.time())
        ).count()

        # 计算本月剩余天数
        import calendar
        days_in_month = calendar.monthrange(hotel.date.year, hotel.date.month)[1]
        days_remaining = days_in_month - hotel.date.day + 1

        # 避免在每月1号触发事件，防止与月度报告冲突
        is_first_day = hotel.date.day == 1
        if is_first_day:
            return None

        # 如果本月事件数少于2个，且剩余天数不多，强制触发事件
        should_force_trigger = (monthly_events < 2 and days_remaining <= 5) or (monthly_events == 0 and days_remaining <= 10)

        # 正常概率触发（每日2%概率，确保每月平均约2个事件）或强制触发
        if not should_force_trigger and random.random() > 0.02:
            return None

        # 检查是否已有活跃事件
        active_events = RandomEvent.query.filter_by(
            hotel_id=hotel.id,
            is_active=True
        ).count()

        # 如果已有3个活跃事件，不再触发新事件（除非强制触发）
        if active_events >= 3 and not should_force_trigger:
            return None
            
        # 随机选择事件类型
        event_type = random.choices(
            ['positive', 'negative', 'neutral'],
            weights=[0.4, 0.4, 0.2]  # 正面40%，负面40%，中性20%
        )[0]
        
        # 从对应类型中随机选择事件
        events = RANDOM_EVENTS[event_type]
        if not events:
            return None
            
        event_config = random.choice(events)
        
        # 创建事件记录
        event = RandomEvent(
            hotel_id=hotel.id,
            name=event_config['name'],
            description=event_config['description'],
            event_type=event_type,
            effect_type=event_config['effect_type'],
            effect_value=event_config['effect_value'],
            duration=event_config['duration'],
            triggered_at=datetime.utcnow(),
            is_active=True,
            shown_to_player=False  # 新事件默认未显示
        )
        
        db.session.add(event)
        
        # 立即应用事件效果
        apply_event_effect(hotel, event)
        
        db.session.commit()
        
        logger.info(f"触发随机事件: {event.name} ({event_type})")
        return event
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"触发随机事件时出错: {e}")
        return None

def apply_event_effect(hotel, event):
    """应用事件效果"""
    try:
        effect_type = event.effect_type
        effect_value = event.effect_value
        
        if effect_type == 'money':
            hotel.money += effect_value
            # 记录财务变化
            if effect_value > 0:
                record = FinancialRecord(
                    hotel_id=hotel.id,
                    record_date=hotel.date,
                    income=effect_value,
                    description=f"随机事件：{event.name}"
                )
            else:
                record = FinancialRecord(
                    hotel_id=hotel.id,
                    record_date=hotel.date,
                    expense=abs(effect_value),
                    description=f"随机事件：{event.name}"
                )
            db.session.add(record)
            
        elif effect_type == 'satisfaction':
            hotel.satisfaction = max(0, min(100, hotel.satisfaction + effect_value))
            
        elif effect_type == 'reputation':
            hotel.reputation += effect_value
            
        elif effect_type == 'employee_quit':
            # 随机解雇员工
            from app.models import Employee
            employees = Employee.query.filter_by(hotel_id=hotel.id).limit(effect_value).all()
            for emp in employees:
                db.session.delete(emp)
                
        # 其他效果类型（如入住率变化）在计算收入时处理
        
        logger.info(f"应用事件效果: {effect_type} = {effect_value}")
        
    except Exception as e:
        logger.error(f"应用事件效果时出错: {e}")

def check_event_expiry(hotel):
    """检查并处理过期事件"""
    try:
        current_date = hotel.date

        # 获取所有活跃事件并检查是否过期
        active_events = RandomEvent.query.filter(
            RandomEvent.hotel_id == hotel.id,
            RandomEvent.is_active == True
        ).all()

        expired_events = []
        for event in active_events:
            # 计算事件是否过期
            event_end_date = event.triggered_at.date() + timedelta(days=event.duration)
            if current_date >= event_end_date:
                event.is_active = False
                expired_events.append(event)
                logger.info(f"事件过期: {event.name}")

        if expired_events:
            db.session.commit()

    except Exception as e:
        db.session.rollback()
        logger.error(f"检查事件过期时出错: {e}")

def get_active_events(hotel):
    """获取当前活跃的事件"""
    try:
        return RandomEvent.query.filter_by(
            hotel_id=hotel.id,
            is_active=True
        ).order_by(RandomEvent.triggered_at.desc()).all()
    except Exception as e:
        logger.error(f"获取活跃事件时出错: {e}")
        return []

def get_event_effects(hotel):
    """获取当前所有活跃事件的效果总和"""
    try:
        active_events = get_active_events(hotel)
        
        effects = {
            'occupancy_boost': 0,
            'occupancy_drop': 0,
            'satisfaction_boost': 0,
            'reputation_boost': 0
        }
        
        for event in active_events:
            if event.effect_type == 'occupancy_boost':
                effects['occupancy_boost'] += event.effect_value
            elif event.effect_type == 'occupancy_drop':
                effects['occupancy_drop'] += abs(event.effect_value)
            elif event.effect_type == 'satisfaction' and event.effect_value > 0:
                effects['satisfaction_boost'] += event.effect_value
            elif event.effect_type == 'reputation' and event.effect_value > 0:
                effects['reputation_boost'] += event.effect_value
                
        return effects
        
    except Exception as e:
        logger.error(f"获取事件效果时出错: {e}")
        return {'occupancy_boost': 0, 'occupancy_drop': 0, 'satisfaction_boost': 0, 'reputation_boost': 0}
