import threading
import time

# 全局变量控制时间线程运行状态
TIME_RUNNING = True
TIME_THREAD = None

from app import db, create_app
from app.models import Hotel, Room, Employee, Department, FinancialRecord, Achievement, SeasonalEffect, RandomEvent
from datetime import datetime, timedelta
from collections import defaultdict
import random
import logging
import time
import threading
from sqlalchemy.exc import OperationalError
import sqlite3

logger = logging.getLogger(__name__)

def safe_database_operation(operation, max_retries=5, base_delay=0.1):
    """
    安全的数据库操作，带重试机制

    Args:
        operation: 要执行的数据库操作函数
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）

    Returns:
        操作结果
    """
    for attempt in range(max_retries):
        try:
            result = operation()
            return result
        except (OperationalError, sqlite3.OperationalError) as e:
            if "database is locked" in str(e).lower() or "locked" in str(e).lower():
                if attempt < max_retries - 1:
                    # 指数退避策略
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    logger.warning(f"数据库锁定，第{attempt + 1}次重试，等待{delay:.2f}秒")
                    db.session.rollback()
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"数据库操作失败，已重试{max_retries}次")
                    db.session.rollback()
                    raise e
            else:
                logger.error(f"数据库操作出错: {e}")
                db.session.rollback()
                raise e
        except Exception as e:
            logger.error(f"操作执行出错: {e}")
            db.session.rollback()
            raise e

    return None

# 房间价格配置（根据优化需求文档修正）
ROOM_PRICES = {
    "单人间": 300,
    "标准间": 500,
    "大床房": 700,
    "家庭房": 1000,
    "商务间": 1500,
    "行政间": 2000,
    "豪华间": 3000,
    "总统套房": 5000,
    "皇家套房": 8000,
    "总统别墅": 15000,
    "皇宫套房": 30000
}


def get_current_season(date):
    """获取当前季节"""
    month = date.month
    if month in [12, 1, 2]:
        return "冬季"
    elif month in [3, 4, 5]:
        return "春季"
    elif month in [6, 7, 8]:
        return "夏季"
    else:
        return "秋季"


# 定义房间类型及其入住率范围（根据优化需求文档修正）
ROOM_OCCUPANCY_RANGES = {
    '单人间': (0.4, 0.8),      # 60% ± 20%
    '标准间': (0.5, 0.9),      # 70% ± 20%
    '大床房': (0.55, 0.95),    # 75% ± 20%
    '家庭房': (0.45, 0.85),    # 65% ± 20%
    '商务间': (0.6, 1.0),      # 80% ± 20%
    '行政间': (0.65, 1.0),     # 85% ± 20%
    '豪华间': (0.7, 1.0),      # 90% ± 20%
    '总统套房': (0.75, 1.0),   # 95% ± 20%
    '皇家套房': (0.72, 1.0),   # 92% ± 20%
    '总统别墅': (0.74, 1.0),   # 94% ± 20%
    '皇宫套房': (0.76, 1.0)    # 96% ± 20%
}

# 员工等级服务能力配置（根据优化需求文档添加）
EMPLOYEE_SERVICE_CAPACITY = {
    "初级": 1.0,    # 初级员工服务能力1.0
    "中级": 1.5,    # 中级员工服务能力1.5
    "高级": 2.0,    # 高级员工服务能力2.0
    "特级": 3.0     # 特级员工服务能力3.0
}


def get_current_hotel():
    """获取当前酒店实例"""
    return Hotel.query.first()


def calculate_stable_occupancy_rate(hotel, room_type, date):
    """计算稳定的入住率（减少随机性）"""
    try:
        # 基础入住率（基于房间类型）
        base_rates = {
            '单人间': 75, '标准间': 80, '大床房': 78, '家庭房': 72,
            '商务间': 85, '行政间': 88, '豪华间': 82, '总统套房': 75,
            '皇家套房': 70, '总统别墅': 65, '皇宫套房': 60
        }
        base_rate = base_rates.get(room_type, 70)

        # 酒店等级影响（每级+2%）
        level_bonus = (hotel.level - 1) * 2

        # 满意度影响（满意度每10分影响±5%）
        satisfaction_effect = (hotel.satisfaction - 50) * 0.1

        # 声望影响（声望等级每级+1%）
        reputation_bonus = hotel.reputation_level * 1

        # 营销活动影响
        marketing_bonus = 0
        try:
            from app.models import MarketingCampaign
            active_campaigns = MarketingCampaign.query.filter_by(
                hotel_id=hotel.id,
                is_active=True
            ).all()
            for campaign in active_campaigns:
                marketing_bonus += campaign.effect
        except:
            pass

        # 季节性影响（减少随机性）
        month_factors = {
            1: -5, 2: -3, 3: 0, 4: 2, 5: 5, 6: 8,
            7: 10, 8: 8, 9: 3, 10: 0, 11: -2, 12: 2
        }
        seasonal_effect = month_factors.get(date.month, 0)

        # 周末加成
        weekend_bonus = 5 if date.weekday() >= 5 else 0

        # 计算最终入住率
        final_rate = base_rate + level_bonus + satisfaction_effect + reputation_bonus + marketing_bonus + seasonal_effect + weekend_bonus

        # 添加小幅随机波动（±2%，而不是之前的大幅波动）
        import random
        random_variation = random.uniform(-2, 2)
        final_rate += random_variation

        # 确保在合理范围内
        return max(20, min(95, final_rate))

    except Exception as e:
        logger.error(f"计算稳定入住率时出错: {e}")
        return 70  # 默认70%


def calculate_daily_finances(hotel):
    """计算每日财务，为每种房间类型创建独立的财务记录"""
    logger.info(f"开始计算 {hotel.date} 的财务")

    # 使用新的数据库解决方案
    from app.main.database_fix import safe_query_with_retry

    # 安全获取酒店的所有房间
    def get_rooms():
        return Room.query.filter_by(hotel_id=hotel.id).all()

    rooms = safe_query_with_retry(get_rooms)
    if not rooms:
        logger.error("无法获取房间数据")
        return False

    total_income = 0

    # 为每种房间类型单独计算收入
    for room in rooms:
        # 确保room.count和room.price是数字类型
        room_count = int(room.count) if room.count and str(room.count).isdigit() else 0
        room_price = int(room.price) if room.price and str(room.price).isdigit() else 0

        if room_count > 0 and room_price > 0:
            # 计算该房间类型的理论最大收入
            max_income = room_count * room_price

            # 根据房间类型确定基础入住率范围
            min_rate, max_rate = ROOM_OCCUPANCY_RANGES.get(room.type, (0.5, 1.0))

            # 使用稳定的入住率计算
            occupancy_rate_percent = calculate_stable_occupancy_rate(hotel, room.type, hotel.date)

            # 四舍五入入住率到整数百分比
            occupancy_rate_rounded = round(occupancy_rate_percent)
            occupancy_rate = occupancy_rate_rounded / 100

            # 计算实际收入（确保是50的倍数）
            actual_income = room_count * room_price * occupancy_rate
            
            # 为每种房间类型创建独立的财务记录
            room_income_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,
                description=f'{room.type}日常经营收入',
                income=int(actual_income),
                expense=0
            )
            db.session.add(room_income_record)
            
            logger.info(f"{room.type}收入: {actual_income}")
            total_income += actual_income
    
    # 计算入住客户数和员工数（用于餐饮和康养收入计算）
    total_guests = 0.0
    for room in rooms:
        # 确保room.count是数字类型
        room_count = int(room.count) if room.count and str(room.count).isdigit() else 0
        if room_count > 0:
            min_rate, max_rate = ROOM_OCCUPANCY_RANGES.get(room.type, (0.5, 1.0))
            occupancy_rate = random.uniform(min_rate, max_rate)
            total_guests += room_count * occupancy_rate

    total_employees = Employee.query.filter_by(hotel_id=hotel.id).count()

    # 检查部门解锁状态
    departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
    unlocked_dept_names = [dept.name for dept in departments]

    # 餐饮收入（需要餐饮部解锁）
    if "餐饮部" in unlocked_dept_names:
        catering_income = total_guests * 20 * hotel.level + total_employees * 5 * hotel.level
        catering_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description='餐饮部日常收入',
            income=int(catering_income),
            expense=0
        )
        db.session.add(catering_record)
        total_income += catering_income
        logger.info(f"餐饮收入: {catering_income}")

    # 康养收入（需要康养部解锁）
    if "康养部" in unlocked_dept_names:
        wellness_income = total_guests * 15 * hotel.level
        wellness_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description='康养部日常收入',
            income=int(wellness_income),
            expense=0
        )
        db.session.add(wellness_record)
        total_income += wellness_income
        logger.info(f"康养收入: {wellness_income}")

    # 董事会加成（需要董事会解锁）
    if "董事会" in unlocked_dept_names:
        board_bonus = total_income * 0.1  # 总收入增加10%
        board_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description='董事会管理加成',
            income=int(board_bonus),
            expense=0
        )
        db.session.add(board_record)
        total_income += board_bonus
        logger.info(f"董事会加成: {board_bonus}")

    # 更新酒店资金
    hotel.money += int(total_income)

    logger.info(f"日期更新: {hotel.date}, 资金: {hotel.money}, 收入: {total_income}")

    return int(total_income)


def update_employees_work_age(hotel):
    """更新员工工龄"""
    try:
        # 更新所有员工的工龄（每年1月1日更新）
        if hotel.date.month == 1 and hotel.date.day == 1:
            employees = Employee.query.filter_by(hotel_id=hotel.id).all()
            updated_count = 0
            for employee in employees:
                employee.years_worked += 1
                updated_count += 1

            db.session.commit()
            logger.info(f"更新了 {updated_count} 名员工的工龄")
        else:
            logger.info(f"更新了 0 名员工的工龄")
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新员工工龄时出错: {e}")


def promote_employees(hotel):
    """年初检查并晋升员工"""
    try:
        # 获取所有员工
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()

        # 定义晋升规则（工龄要求）
        promotion_rules = {
            "初级": 2,   # 初级员工工作2年后可晋升为中级
            "中级": 3,   # 中级员工工作3年后可晋升为高级
            "高级": 5,   # 高级员工工作5年后可晋升为特级
        }

        promoted_count = 0
        for employee in employees:
            # 检查是否满足晋升条件
            if employee.level in promotion_rules and employee.years_worked >= promotion_rules[employee.level]:
                # 晋升员工
                if employee.level == "初级":
                    employee.level = "中级"
                elif employee.level == "中级":
                    employee.level = "高级"
                elif employee.level == "高级":
                    employee.level = "特级"

                # 重置工龄（修复：使用正确的字段名）
                employee.years_worked = 0
                promoted_count += 1
                logger.info(f"员工 {employee.name} 晋升到 {employee.level}")

        db.session.commit()
        logger.info(f"晋升了 {promoted_count} 名员工")
    except Exception as e:
        db.session.rollback()
        logger.error(f"晋升员工时出错: {e}")


def deduct_monthly_salaries(hotel):
    """月初扣除员工工资"""
    try:
        # 确保在函数作用域内导入所需模型
        from app.models import Room, Department, FinancialRecord

        # 获取所有员工
        employees = Employee.query.filter_by(hotel_id=hotel.id).all()
        
        total_salary = 0
        salary_records = []
        
        # 计算每个员工的工资（根据优化需求文档修正）
        for employee in employees:
            # 基础工资根据员工等级确定
            base_salary = {
                "初级": 3000,   # 初级员工基础工资3000元/月
                "中级": 5000,   # 中级员工基础工资5000元/月
                "高级": 8000,   # 高级员工基础工资8000元/月
                "特级": 15000   # 特级员工基础工资15000元/月
            }.get(employee.level, 3000)

            # 工龄加成（每年工龄+20%，最多10年）
            years_worked = getattr(employee, 'years_worked', 0)
            max_years = min(years_worked, 10)  # 工龄上限10年
            seniority_multiplier = 1 + (max_years * 0.2)

            # 计算总工资（设置工资上限）
            salary = base_salary * seniority_multiplier

            # 工资上限设置
            salary_caps = {
                "初级": 9000,    # 初级员工工资上限9000元
                "中级": 15000,   # 中级员工工资上限15000元
                "高级": 24000,   # 高级员工工资上限24000元
                "特级": 45000    # 特级员工工资上限45000元
            }

            salary_cap = salary_caps.get(employee.level, 9000)
            salary = min(salary, salary_cap)
            total_salary += salary
            
            # 记录工资明细
            salary_records.append({
                "employee_name": employee.name,
                "level": employee.level,
                "years_worked": years_worked,
                "salary": salary
            })

        # 检查财务部是否解锁（财务部解锁后所有支出减少5%）
        finance_dept = Department.query.filter_by(
            hotel_id=hotel.id,
            name="财务部",
            is_unlocked=True
        ).first()

        if finance_dept:
            total_salary *= 0.95  # 财务部优化，支出减少5%
            logger.info("财务部优化效果：工资支出减少5%")

        # 创建工资支出记录
        salary_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description="员工工资支出",
            income=0,
            expense=int(total_salary)
        )
        db.session.add(salary_record)

        # 扣除酒店资金
        hotel.money -= int(total_salary)

        # 水电费支出（基于房间数量和入住率）
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        total_rooms = sum(room.count for room in rooms)

        # 估算入住客人数
        total_guests = int(total_rooms * 0.7)  # 简化估算
        utilities_cost = total_rooms * 50 * hotel.level + total_guests * 10  # 基础水电费 + 客人使用费

        # 获取已解锁的部门
        unlocked_departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
        unlocked_dept_names = [dept.name for dept in unlocked_departments]

        # 工程部解锁后水电费减少20%
        if "工程部" in unlocked_dept_names:
            utilities_cost *= 0.8

        utilities_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description="水电费支出",
            income=0,
            expense=int(utilities_cost)
        )
        db.session.add(utilities_record)
        hotel.money -= int(utilities_cost)

        # 房间维护费用（按各房间价格的20%/间收取）
        maintenance_cost = 0
        for room in rooms:
            room_count = int(room.count) if room.count else 0
            # 获取房间价格
            room_prices = {
                "单人间": 300, "标准间": 500, "大床房": 600, "家庭房": 800,
                "商务间": 1000, "行政间": 1200, "豪华间": 1500, "总统套房": 2000,
                "皇家套房": 3000, "总统别墅": 5000, "皇宫套房": 8000
            }
            room_price = room_prices.get(room.type, 300)
            # 每间房按价格的20%收取日维护费
            maintenance_cost += room_count * room_price * 0.2

        # 工程部解锁后维护费减少15%
        if "工程部" in unlocked_dept_names:
            maintenance_cost *= 0.85

        maintenance_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            description="房间维护费用",
            income=0,
            expense=int(maintenance_cost)
        )
        db.session.add(maintenance_record)
        hotel.money -= int(maintenance_cost)

        # 部门运营费用（每个解锁的部门都有运营成本）
        for dept_name in unlocked_dept_names:
            dept_cost = hotel.level * 1000  # 基础部门运营费用

            # 不同部门有不同的运营成本倍数
            dept_multipliers = {
                "前台": 1.0,
                "客房部": 1.2,
                "营销部": 1.5,
                "餐饮部": 2.0,
                "安保部": 1.3,
                "财务部": 1.1,
                "商务部": 1.4,
                "工程部": 1.6,
                "康养部": 1.8,
                "董事会": 2.5
            }

            multiplier = dept_multipliers.get(dept_name, 1.0)
            final_dept_cost = dept_cost * multiplier

            # 财务部解锁后所有部门运营费用减少10%
            if "财务部" in unlocked_dept_names and dept_name != "财务部":
                final_dept_cost *= 0.9

            dept_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,
                description=f"{dept_name}运营费用",
                income=0,
                expense=int(final_dept_cost)
            )
            db.session.add(dept_record)
            hotel.money -= int(final_dept_cost)

        # 使用批量提交优化性能
        try:
            db.session.commit()
            logger.info(f"扣除员工工资共计: {total_salary}")
            logger.info(f"水电费支出: {utilities_cost}")
            logger.info(f"维护费支出: {maintenance_cost}")
        except Exception as commit_error:
            db.session.rollback()
            logger.error(f"数据库提交失败: {commit_error}")
            # 尝试分批提交
            try:
                # 先提交酒店资金变更
                hotel.money -= int(total_salary)
                db.session.commit()
                logger.info("酒店资金变更提交成功")
            except Exception as hotel_commit_error:
                db.session.rollback()
                logger.error(f"酒店资金变更提交失败: {hotel_commit_error}")
                raise
        
        # 记录详细工资信息到日志
        for record in salary_records:
            logger.info(f"员工 {record['employee_name']} ({record['level']}, 工龄{record['years_worked']}年) 工资: {record['salary']}")
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"扣除员工工资时出错: {e}")


def deduct_room_maintenance_fees(hotel):
    """月初扣除房间维护费用（根据优化需求文档添加）"""
    try:
        # 获取所有房间
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        total_rooms = sum(room.count for room in rooms)

        if total_rooms > 0:
            # 基础维护费用：每间房间每月100元
            base_maintenance_cost = total_rooms * 100

            # 检查工程部是否解锁（工程部解锁后维护费用减少50%）
            engineering_dept = Department.query.filter_by(
                hotel_id=hotel.id,
                name="工程部",
                is_unlocked=True
            ).first()

            if engineering_dept:
                base_maintenance_cost *= 0.5  # 工程部优化，维护费用减少50%
                logger.info("工程部优化效果：房间维护费用减少50%")

            # 检查财务部是否解锁（财务部解锁后所有支出减少5%）
            finance_dept = Department.query.filter_by(
                hotel_id=hotel.id,
                name="财务部",
                is_unlocked=True
            ).first()

            if finance_dept:
                base_maintenance_cost *= 0.95  # 财务部优化，支出减少5%
                logger.info("财务部优化效果：维护费用减少5%")

            # 创建维护费用支出记录
            maintenance_record = FinancialRecord(
                hotel_id=hotel.id,
                record_date=hotel.date,
                description=f"房间维护费用（{total_rooms}间）",
                income=0,
                expense=int(base_maintenance_cost)
            )
            db.session.add(maintenance_record)

            # 扣除酒店资金
            hotel.money -= int(base_maintenance_cost)

            db.session.commit()
            logger.info(f"扣除房间维护费用: {int(base_maintenance_cost)}元")

    except Exception as e:
        db.session.rollback()
        logger.error(f"扣除房间维护费用时出错: {e}")


def generate_monthly_report(hotel):
    """生成月度财务报告"""
    try:
        from app.models import FinancialRecord
        from datetime import datetime
        import calendar

        # 计算上个月的日期范围
        current_date = hotel.date
        if current_date.month == 1:
            last_month = 12
            last_year = current_date.year - 1
        else:
            last_month = current_date.month - 1
            last_year = current_date.year

        # 上个月的第一天和最后一天
        first_day = current_date.replace(year=last_year, month=last_month, day=1)
        last_day_num = calendar.monthrange(last_year, last_month)[1]
        last_day = current_date.replace(year=last_year, month=last_month, day=last_day_num)

        # 获取上个月的财务记录
        monthly_records = FinancialRecord.query.filter(
            FinancialRecord.hotel_id == hotel.id,
            FinancialRecord.record_date >= first_day,
            FinancialRecord.record_date <= last_day
        ).all()

        # 计算收入和支出
        total_income = sum(record.income for record in monthly_records if record.income)
        total_expense = sum(record.expense for record in monthly_records if record.expense)
        net_profit = total_income - total_expense

        # 按类型分类收入和支出
        income_categories = {}
        expense_categories = {}

        for record in monthly_records:
            if record.income and record.income > 0:
                category = record.description.split('收入')[0] if '收入' in record.description else '其他收入'
                income_categories[category] = income_categories.get(category, 0) + record.income
            elif record.expense and record.expense > 0:
                category = record.description.split('费用')[0] if '费用' in record.description else record.description
                expense_categories[category] = expense_categories.get(category, 0) + record.expense

        # 创建月度报告数据
        month_str = f"{last_year}-{last_month:02d}"

        report_data = {
            'month_str': month_str,
            'year': last_year,
            'month': last_month,
            'total_income': total_income,
            'total_expense': total_expense,
            'net_profit': net_profit,
            'income_categories': income_categories,
            'expense_categories': expense_categories,
            'record_count': len(monthly_records)
        }

        # 将报告数据存储到全局变量中，供前端获取
        global MONTHLY_REPORT_DATA
        MONTHLY_REPORT_DATA = report_data

        logger.info(f"生成月度财务报告: {month_str} - 收入:{total_income}, 支出:{total_expense}, 净利润:{net_profit}")

    except Exception as e:
        logger.error(f"生成月度财务报告时出错: {e}")


# 全局变量存储月度报告数据
MONTHLY_REPORT_DATA = None


def calculate_satisfaction_and_reputation(hotel):
    """计算客户满意度和酒店声望值（根据优化需求文档修正）"""
    try:
        # 基础满意度：从50分开始，主要通过运营表现来提升
        satisfaction = 50.0

        # 酒店星级基础加成：每星+2分（降低固定加成，增加可玩性）
        satisfaction += hotel.level * 2

        # 入住率影响满意度（新增）
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        total_occupancy = 0
        total_rooms = 0
        for room in rooms:
            room_count = int(room.count) if room.count else 0
            if room_count > 0:
                occupancy_rate = calculate_stable_occupancy_rate(hotel, room.type, hotel.date)
                total_occupancy += occupancy_rate * room_count
                total_rooms += room_count

        if total_rooms > 0:
            avg_occupancy = total_occupancy / total_rooms
            # 入住率对满意度的影响：高入住率说明服务受欢迎
            if avg_occupancy >= 90:
                satisfaction += 15  # 超高入住率：+15分
            elif avg_occupancy >= 80:
                satisfaction += 10  # 高入住率：+10分
            elif avg_occupancy >= 70:
                satisfaction += 5   # 良好入住率：+5分
            elif avg_occupancy >= 60:
                satisfaction += 0   # 一般入住率：+0分
            elif avg_occupancy >= 50:
                satisfaction -= 5   # 较低入住率：-5分
            else:
                satisfaction -= 10  # 低入住率：-10分

        # 房间等级加成
        room_bonus = 0.0
        for room in rooms:
            # 确保room.count是整数类型
            room_count = int(room.count) if room.count else 0
            if room.type == "皇宫套房":
                room_bonus += 10.0 * room_count
            elif room.type == "总统别墅":
                room_bonus += 8.0 * room_count
            elif room.type == "皇家套房":
                room_bonus += 6.0 * room_count
            elif room.type == "总统套房":
                room_bonus += 4.0 * room_count
            elif room.type == "豪华间":
                room_bonus += 2.0 * room_count

        # 房间加成按总房间数平均
        total_rooms = sum(int(room.count) if room.count else 0 for room in rooms)
        if total_rooms > 0:
            satisfaction += room_bonus / total_rooms

        # 员工数量加成：充足的员工提升满意度
        total_employees = Employee.query.filter_by(hotel_id=hotel.id).count()
        total_rooms = sum(int(room.count) if room.count else 0 for room in rooms)

        if total_rooms > 0:
            # 员工与房间比例加成
            employee_ratio = total_employees / total_rooms
            if employee_ratio >= 0.5:  # 每2间房至少1个员工
                satisfaction += 8  # 员工充足：+8分
            elif employee_ratio >= 0.3:  # 每3间房至少1个员工
                satisfaction += 5  # 员工较充足：+5分
            elif employee_ratio >= 0.2:  # 每5间房至少1个员工
                satisfaction += 2  # 员工基本够用：+2分
            else:
                satisfaction -= 5  # 员工不足：-5分

        # 部门特殊效果（增加加成）
        departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
        unlocked_dept_count = len(departments)

        # 部门数量加成：每个解锁的部门+1分
        satisfaction += unlocked_dept_count

        for dept in departments:
            if dept.name == "安保部":
                satisfaction += 8  # 安保部：+8分（增加）
            elif dept.name == "康养部":
                satisfaction += 12  # 康养部：+12分（增加）
            elif dept.name == "餐饮部":
                satisfaction += 6  # 餐饮部：+6分
            elif dept.name == "客房部":
                satisfaction += 4  # 客房部：+4分
            elif dept.name == "营销部":
                satisfaction += 3  # 营销部：+3分

        # 部门繁忙度影响（只计算已解锁部门）
        busy_levels = calculate_department_busy_level(hotel)
        unlocked_departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).all()
        unlocked_dept_names = [dept.name for dept in unlocked_departments]

        # 只计算已解锁部门的繁忙度
        valid_levels = []
        for dept_name, level in busy_levels.items():
            if dept_name in unlocked_dept_names:
                try:
                    level_float = float(level) if level is not None else 0.0
                    if level_float > 0:
                        valid_levels.append(level_float)
                except (ValueError, TypeError):
                    continue

        avg_busy_level = sum(valid_levels) / len(valid_levels) if valid_levels else 0.0

        if avg_busy_level < 50:
            satisfaction += 10
        elif avg_busy_level <= 80:
            satisfaction += 0
        elif avg_busy_level <= 100:
            satisfaction -= 5
        else:
            satisfaction -= 15

        # 限制满意度在0-100之间
        satisfaction = max(0, min(100, satisfaction))

        # 更新酒店满意度
        hotel.satisfaction = round(satisfaction, 2)
        
        # 计算声望值变化
        reputation_change = 0
        
        # 根据满意度等级调整声望（等级制）
        satisfaction_level = get_satisfaction_level(satisfaction)
        satisfaction_reputation = {
            '极差': -5,    # 0-29分
            '很差': -3,    # 30-39分
            '较差': -1,    # 40-49分
            '一般': 0,     # 50-59分
            '良好': 2,     # 60-69分
            '优秀': 5,     # 70-79分
            '卓越': 8,     # 80-89分
            '完美': 12     # 90-100分
        }.get(satisfaction_level, 0)

        reputation_change += satisfaction_reputation
        
        # 根据财务表现等级调整声望（等级制）
        # 获取今天的财务记录
        today_records = FinancialRecord.query.filter_by(
            hotel_id=hotel.id,
            record_date=hotel.date
        ).all()

        daily_profit = sum(record.income - record.expense for record in today_records)
        financial_level = get_financial_performance_level(daily_profit)
        financial_reputation = {
            '严重亏损': -8,    # < -500,000
            '重大亏损': -5,    # -500,000 ~ -100,000
            '中等亏损': -3,    # -100,000 ~ -50,000
            '轻微亏损': -1,    # -50,000 ~ -10,000
            '微亏': 0,         # -10,000 ~ 0
            '微盈': 1,         # 0 ~ 10,000
            '小盈': 2,         # 10,000 ~ 50,000
            '中盈': 4,         # 50,000 ~ 100,000
            '大盈': 6,         # 100,000 ~ 500,000
            '巨盈': 10,        # 500,000 ~ 1,000,000
            '超盈': 15         # > 1,000,000
        }.get(financial_level, 0)

        reputation_change += financial_reputation
        
        # 更新声望值
        hotel.reputation = max(0, hotel.reputation + reputation_change)
        
        # 根据声望值确定声望等级（根据优化需求文档修正）
        reputation_levels = [
            (499, "默默无闻"),
            (1999, "小有名气"),
            (4999, "知名酒店"),
            (9999, "著名品牌"),
            (19999, "行业标杆"),
            (49999, "行业领袖"),
            (99999, "国际知名"),
            (float('inf'), "传奇酒店")
        ]
        
        for threshold, level_name in reputation_levels:
            if hotel.reputation < threshold:
                hotel.reputation_level_name = level_name
                break
        
        # 计算声望等级（0-10的整数）
        hotel.reputation_level = min(10, hotel.reputation // 1000)
        
        db.session.commit()
        logger.info(f"更新满意度: {hotel.satisfaction}, 声望: {hotel.reputation} ({hotel.reputation_level_name})")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"计算满意度和声望时出错: {e}")


def get_satisfaction_level(satisfaction):
    """根据满意度分数获取满意度等级"""
    if satisfaction >= 90:
        return '完美'
    elif satisfaction >= 80:
        return '卓越'
    elif satisfaction >= 70:
        return '优秀'
    elif satisfaction >= 60:
        return '良好'
    elif satisfaction >= 50:
        return '一般'
    elif satisfaction >= 40:
        return '较差'
    elif satisfaction >= 30:
        return '很差'
    else:
        return '极差'

def get_financial_performance_level(daily_profit):
    """根据日利润获取财务表现等级"""
    if daily_profit > 1000000:
        return '超盈'
    elif daily_profit > 500000:
        return '巨盈'
    elif daily_profit > 100000:
        return '大盈'
    elif daily_profit > 50000:
        return '中盈'
    elif daily_profit > 10000:
        return '小盈'
    elif daily_profit > 0:
        return '微盈'
    elif daily_profit > -10000:
        return '微亏'
    elif daily_profit > -50000:
        return '轻微亏损'
    elif daily_profit > -100000:
        return '中等亏损'
    elif daily_profit > -500000:
        return '重大亏损'
    else:
        return '严重亏损'

def calculate_department_busy_level(hotel):
    """计算部门繁忙度"""
    try:
        busy_levels = {}
        
        # 获取所有部门
        departments = Department.query.filter_by(hotel_id=hotel.id).all()
        
        # 估算客户数（基于房间入住情况）
        rooms = Room.query.filter_by(hotel_id=hotel.id).all()
        estimated_guests = 0
        for room in rooms:
            # 使用实际入住率计算
            occupancy_rate = calculate_stable_occupancy_rate(hotel, room.type, hotel.date) / 100
            # 所有房型都按1人/房计算，简化计算
            guests_per_room = 1

            # 确保room.count是数字类型
            room_count = int(room.count) if room.count and str(room.count).isdigit() else 0
            estimated_guests += room_count * occupancy_rate * guests_per_room
        
        # 计算各部门繁忙度
        for department in departments:
            # 各部门基础服务能力（每人每天能服务的客户数）
            # 前台部和客房部需要更多人手，服务能力较低
            # 其他部门特别是高级部门服务能力较高
            capacity_per_employee = {
                '前台部': 15,    # 前台需要更多人手
                '客房部': 10,    # 客房清洁需要更多人手
                '餐饮部': 25,
                '营销部': 40,
                '安保部': 50,
                '财务部': 60,    # 高级部门服务能力强
                '商务部': 80,    # 高级部门服务能力强
                '康养部': 70,    # 高级部门服务能力强
                '工程部': 100,   # 高级部门服务能力强
                '人事部': 50,
                '董事会': 150    # 最高级部门服务能力最强
            }
            
            # 获取部门员工并计算总服务能力
            dept_employees = Employee.query.filter_by(
                hotel_id=hotel.id,
                department=department.name
            ).all()

            if dept_employees:
                # 计算部门总服务能力（考虑员工等级）
                total_service_capacity = 0
                for employee in dept_employees:
                    # 基础服务能力 × 员工等级系数
                    base_capacity = capacity_per_employee.get(department.name, 20)
                    level_multiplier = EMPLOYEE_SERVICE_CAPACITY.get(employee.level, 1.0)
                    total_service_capacity += base_capacity * level_multiplier

                # 计算繁忙度（客户数/总服务能力）
                # 如果服务能力为0，按1计算，避免除零错误和不合理的0%繁忙度
                effective_capacity = max(1, total_service_capacity)
                busy_level = min(200, (estimated_guests / effective_capacity) * 100)
                busy_levels[department.name] = busy_level
            else:
                # 没有员工的部门，按最小服务能力1计算繁忙度
                busy_level = min(200, estimated_guests * 100)  # 相当于除以1
                busy_levels[department.name] = busy_level
        
        return busy_levels
    except Exception as e:
        logger.error(f"计算部门繁忙度时出错: {e}")
        return {}


def check_random_events(hotel):
    """检查并触发随机事件"""
    try:
        # 定义一些随机事件
        events = [
            {
                "type": "正面",
                "description": "旅游旺季到来，入住率大幅提升",
                "effect_type": "入住率调整",
                "effect_value": 30.0,  # 提升30%入住率
                "duration": 7  # 持续7天
            },
            {
                "type": "负面",
                "description": "附近新开业一家竞争酒店，入住率下降",
                "effect_type": "入住率调整",
                "effect_value": -20.0,  # 下降20%入住率
                "duration": 10  # 持续10天
            },
            {
                "type": "正面",
                "description": "酒店服务质量获得媒体报道，声望提升",
                "effect_type": "声望调整",
                "effect_value": 500.0,  # 增加500声望值
                "duration": 1  # 立即生效
            },
            {
                "type": "负面",
                "description": "酒店设备出现故障，需要维修支出",
                "effect_type": "支出",
                "effect_value": 5000.0,  # 支出5000元
                "duration": 1  # 立即生效
            },
            {
                "type": "正面",
                "description": "酒店承办重要会议，收入增加",
                "effect_type": "收入",
                "effect_value": 20000.0,  # 增加20000元收入
                "duration": 1  # 立即生效
            }
        ]
        
        # 10%的概率触发随机事件
        if random.random() < 0.1:
            # 随机选择一个事件
            event_data = random.choice(events)
            
            # 创建随机事件记录
            event = RandomEvent(
                hotel_id=hotel.id,
                event_type=event_data["type"],
                description=event_data["description"],
                effect_value=event_data["effect_value"],
                start_date=hotel.date,
                end_date=hotel.date + timedelta(days=event_data["duration"])
            )
            
            db.session.add(event)
            db.session.commit()
            
            logger.info(f"触发随机事件: {event_data['description']}")
            
            # 如果是立即生效的事件，直接应用效果
            if event_data["duration"] == 1:
                if event_data["effect_type"] == "声望调整":
                    hotel.reputation += event_data["effect_value"]
                elif event_data["effect_type"] == "支出":
                    hotel.money -= event_data["effect_value"]
                    # 记录支出
                    expense_record = FinancialRecord(
                        hotel_id=hotel.id,
                        record_date=hotel.date,
                        description=f"随机事件支出: {event_data['description']}",
                        income=0,
                        expense=int(event_data["effect_value"])
                    )
                    db.session.add(expense_record)
                elif event_data["effect_type"] == "收入":
                    hotel.money += event_data["effect_value"]
                    # 记录收入
                    income_record = FinancialRecord(
                        hotel_id=hotel.id,
                        record_date=hotel.date,
                        description=f"随机事件收入: {event_data['description']}",
                        income=int(event_data["effect_value"]),
                        expense=0
                    )
                    db.session.add(income_record)
                
                db.session.commit()
            
            return event_data
            
    except Exception as e:
        db.session.rollback()
        logger.error(f"检查随机事件时出错: {e}")
    
    return None


def time_advance(hotel):
    """推进一天"""
    try:
        # 使用no_autoflush避免自动提交，防止数据库锁定
        with db.session.no_autoflush:
            # 更新日期
            hotel.date += timedelta(days=1)
            hotel.days_elapsed += 1

            # 计算每日财务
            if not calculate_daily_finances(hotel):
                return False

            # 更新员工工龄
            update_employees_work_age(hotel)

            # 年初检查（1月1日）
            if hotel.date.month == 1 and hotel.date.day == 1:
                promote_employees(hotel)

            # 月初检查（1号）
            if hotel.date.day == 1:
                deduct_monthly_salaries(hotel)
                deduct_room_maintenance_fees(hotel)
                # 生成上个月的财务报告
                generate_monthly_report(hotel)

            # 更新满意度和声望
            calculate_satisfaction_and_reputation(hotel)

            # 检查营销活动过期
            try:
                from app.models import MarketingCampaign
                expired_campaigns = MarketingCampaign.query.filter(
                    MarketingCampaign.hotel_id == hotel.id,
                    MarketingCampaign.is_active == True,
                    MarketingCampaign.ends_at < hotel.date
                ).all()

                for campaign in expired_campaigns:
                    campaign.is_active = False
                    logger.info(f"营销活动过期: {campaign.name}")

            except Exception as marketing_error:
                logger.warning(f"营销活动检查失败: {marketing_error}")

            # 检查随机事件（新系统）
            try:
                from app.main.random_events import trigger_random_event, check_event_expiry
                check_event_expiry(hotel)
                event = trigger_random_event(hotel)
                if event:
                    logger.info(f"触发随机事件: {event.name}")
            except Exception as event_error:
                logger.warning(f"随机事件检查失败: {event_error}")

            # 检查成就（新系统）
            try:
                from app.achievements.views import check_achievements as check_new_achievements
                newly_achieved = check_new_achievements(hotel)
                if newly_achieved:
                    logger.info(f"新解锁成就: {[a.name for a in newly_achieved]}")
            except Exception as achievement_error:
                logger.warning(f"新成就系统检查失败: {achievement_error}")

            # 检查旧成就系统（保持兼容）
            try:
                check_achievements(hotel)
            except Exception as legacy_error:
                logger.warning(f"旧成就系统检查失败: {legacy_error}")

        # 直接提交，数据库已优化，不再需要重试机制
        db.session.commit()
        logger.info(f"时间已推进到: {hotel.date}")
        return True

    except Exception as e:
        logger.error(f"时间推进时出错: {e}", exc_info=True)
        db.session.rollback()
        return False


def check_achievements(hotel):
    """检查并解锁成就"""
    try:
        # 资产过亿 - 拥有1亿元资产
        billionaire = Achievement.query.filter_by(hotel_id=hotel.id, name='资产过亿').first()
        if billionaire and not billionaire.achieved:
            if hotel.money >= 100000000:  # 1亿
                billionaire.achieved = True
                billionaire.achieved_date = hotel.date
                logger.info(f"达成成就: {billionaire.name}")

        # 十星级大酒店 - 酒店达到10星
        ten_stars = Achievement.query.filter_by(hotel_id=hotel.id, name='十星级大酒店').first()
        if ten_stars and not ten_stars.achieved:
            if hotel.level >= 10:
                ten_stars.achieved = True
                ten_stars.achieved_date = hotel.date
                logger.info(f"达成成就: {ten_stars.name}")

        # 声望卓著 - 声望值达到50000
        prestigious = Achievement.query.filter_by(hotel_id=hotel.id, name='声望卓著').first()
        if prestigious and not prestigious.achieved:
            if hotel.reputation >= 50000:
                prestigious.achieved = True
                prestigious.achieved_date = hotel.date
                logger.info(f"达成成就: {prestigious.name}")

        # 千人规模 - 雇佣员工数达到1000人
        thousand_employees = Achievement.query.filter_by(hotel_id=hotel.id, name='千人规模').first()
        if thousand_employees and not thousand_employees.achieved:
            employee_count = Employee.query.filter_by(hotel_id=hotel.id).count()
            if employee_count >= 1000:
                thousand_employees.achieved = True
                thousand_employees.achieved_date = hotel.date
                logger.info(f"达成成就: {thousand_employees.name}")

        # 百间客房 - 拥有客房数量达到100间
        hundred_rooms = Achievement.query.filter_by(hotel_id=hotel.id, name='百间客房').first()
        if hundred_rooms and not hundred_rooms.achieved:
            room_count = sum(room.count for room in Room.query.filter_by(hotel_id=hotel.id).all())
            if room_count >= 100:
                hundred_rooms.achieved = True
                hundred_rooms.achieved_date = hotel.date
                logger.info(f"达成成就: {hundred_rooms.name}")

        # 五星级大酒店 - 酒店达到5星
        five_stars = Achievement.query.filter_by(hotel_id=hotel.id, name='五星级大酒店').first()
        if five_stars and not five_stars.achieved:
            if hotel.level >= 5:
                five_stars.achieved = True
                five_stars.achieved_date = hotel.date
                logger.info(f"达成成就: {five_stars.name}")

        # 部门齐全 - 拥有全部10个部门
        all_departments = Achievement.query.filter_by(hotel_id=hotel.id, name='部门齐全').first()
        if all_departments and not all_departments.achieved:
            department_count = Department.query.filter_by(hotel_id=hotel.id).count()
            if department_count >= 10:  # 全部10个部门
                all_departments.achieved = True
                all_departments.achieved_date = hotel.date
                logger.info(f"达成成就: {all_departments.name}")

        db.session.commit()
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"检查成就时出错: {str(e)}")


def start_time_thread():
    """启动时间推进线程"""
    global TIME_RUNNING, TIME_THREAD

    # 如果已经有线程在运行，先停止它
    if TIME_THREAD and TIME_THREAD.is_alive():
        stop_time_thread()
        # 等待线程结束
        TIME_THREAD.join(timeout=5)

    TIME_RUNNING = True
    TIME_THREAD = threading.Thread(target=time_thread_function, daemon=True)
    TIME_THREAD.start()
    logger.info("新的时间推进线程已启动")
    return TIME_THREAD


def time_thread_function():
    """时间推进线程函数"""
    global TIME_RUNNING
    logger.info("时间推进线程启动，运行状态: %s", TIME_RUNNING)

    # 获取应用实例
    from app import create_app, db
    app = create_app()

    # 在应用上下文中运行
    with app.app_context():
        while TIME_RUNNING:
            try:
                # 获取酒店信息
                from app.models import Hotel
                hotel = Hotel.query.first()

                if hotel and hotel.time_running:
                    logger.info("推进时间中... 当前日期: %s, 速度: %d倍速", hotel.date, hotel.time_speed or 1)

                    # 推进一天
                    try:
                        result = time_advance(hotel)
                        if result:
                            logger.info("时间已推进到: %s", hotel.date)
                        else:
                            logger.error("时间推进失败")
                    except Exception as advance_error:
                        logger.error("时间推进出错: %s", advance_error)
                        db.session.rollback()

                    # 根据time_speed调整等待时间
                    speed = hotel.time_speed or 1
                    sleep_time = 5.0 / speed  # 1倍速5秒，2倍速2.5秒
                    logger.debug("等待 %.1f 秒后继续推进", sleep_time)
                    time.sleep(sleep_time)
                else:
                    # 时间暂停时等待1秒
                    time.sleep(1)

            except Exception as e:
                logger.error(f"时间推进线程出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(5)  # 出错时等待5秒后继续


def stop_time_thread():
    """停止时间推进线程"""
    global TIME_RUNNING, TIME_THREAD
    TIME_RUNNING = False
    logger.info("正在停止时间推进线程...")

    # 等待当前线程结束
    if TIME_THREAD and TIME_THREAD.is_alive():
        TIME_THREAD.join(timeout=5)
        if TIME_THREAD.is_alive():
            logger.warning("时间线程未能在5秒内停止")
        else:
            logger.info("时间推进线程已成功停止")
    else:
        logger.info("没有运行中的时间线程")