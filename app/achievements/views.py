from flask import render_template, request, jsonify
from app import db
from app.models import Hotel, Achievement, FinancialRecord, Employee, Room, Department
from app.main.utils import get_current_hotel
import logging

from app.achievements import bp

logger = logging.getLogger(__name__)


@bp.route('/management')
def management():
    """成就管理页面"""
    hotel = get_current_hotel()
    if not hotel:
        return render_template('error.html', message='酒店数据未初始化')
    
    # 获取所有成就并按分类组织
    achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()
    
    # 初始化成就分组
    achievement_groups = {
        '财务成就': [],
        '员工成就': [],
        '发展成就': [],
        '经营成就': [],
        '特殊成就': []
    }
    
    # 定义各类成就名称列表
    achievement_categories = {
        '财务成就': [
            '初次盈利', '百万富翁', '千万富翁', '亿万富翁', '月入百万',
            '月入千万', '年度盈利王', '连续盈利', '财务大师', '投资专家'
        ],
        '员工成就': [
            '首位员工', '百人团队', '千人团队', '万人团队', '人才伯乐',
            '培训大师', '高薪一族', '人事专家', '员工满意', '团队建设'
        ],
        '发展成就': [
            '星光初现', '三星荣耀', '四海为家', '五星级别', '六六大顺',
            '七星高照', '八方来客', '九霄云外', '部门齐全', '房间帝国'
        ],
        '经营成就': [
            '客满为患', '满意服务', '声望卓著', '营销专家', '广告大王',
            '好评如潮', '生意兴隆', '稳定发展', '高端客户', '品牌价值'
        ],
        '特殊成就': [
            '时间管理大师', '存档专家', '探索者', '完美主义者', '长期经营',
            '快速发展', '节约大师', '平衡大师', '幸运之星', '挑战者'
        ]
    }
    
    # 将成就按分类组织
    for achievement in achievements:
        for category, achievements_list in achievement_categories.items():
            if achievement.name in achievements_list:
                achievement_groups[category].append(achievement)
                break  # 找到匹配后跳出循环
    
    # 获取所有成就用于统计
    all_achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()

    return render_template('achievements.html',
                         hotel=hotel,
                         achievement_groups=achievement_groups,
                         achievements=all_achievements)


@bp.route('/claim_reward/<int:achievement_id>', methods=['POST'])
def claim_reward(achievement_id):
    """领取成就奖励"""
    hotel = get_current_hotel()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500
    
    # 查找成就
    achievement = Achievement.query.filter_by(
        id=achievement_id, 
        hotel_id=hotel.id,
        achieved=True,
        reward_claimed=False
    ).first()
    
    if not achievement:
        return jsonify({'success': False, 'message': '成就不存在或奖励已领取'}), 404
    
    # 根据成就名称给予奖励
    reward_amount = 0
    if achievement.name == '初次盈利':
        reward_amount = 10000
    elif achievement.name == '百万富翁':
        reward_amount = 50000
    elif achievement.name == '千万富翁':
        reward_amount = 200000
    elif achievement.name == '亿万富翁':
        reward_amount = 1000000
    elif achievement.name == '首位员工':
        reward_amount = 20000
    elif achievement.name == '星光初现':
        reward_amount = 50000
    elif achievement.name == '三星荣耀':
        reward_amount = 100000
    elif achievement.name == '五星级别':
        reward_amount = 500000
    elif achievement.name == '部门齐全':
        reward_amount = 1000000
    
    # 发放奖励
    if reward_amount > 0:
        hotel.money += reward_amount
        # 记录财务记录，使用游戏时间作为记录日期
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,  # 使用游戏时间作为记录日期
            description=f'成就奖励: {achievement.name}',
            income=reward_amount
        )
        db.session.add(financial_record)
    
    # 标记奖励已领取
    achievement.reward_claimed = True
    
    try:
        db.session.commit()
        return jsonify({'success': True, 'message': f'成功领取奖励 ¥{reward_amount:,}'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '奖励领取失败'}), 500


def init_achievements(hotel):
    """初始化默认成就"""
    achievements_data = [
        # 财务成就 (10个)
        {'name': '初次盈利', 'description': '酒店资金超过初始资金', 'category': '财务成就'},
        {'name': '百万富翁', 'description': '酒店资金达到100万元', 'category': '财务成就'},
        {'name': '千万富翁', 'description': '酒店资金达到1000万元', 'category': '财务成就'},
        {'name': '亿万富翁', 'description': '酒店资金达到1亿元', 'category': '财务成就'},
        {'name': '月入百万', 'description': '单月收入达到100万元', 'category': '财务成就'},
        {'name': '月入千万', 'description': '单月收入达到1000万元', 'category': '财务成就'},
        {'name': '年度盈利王', 'description': '年度总盈利达到1亿元', 'category': '财务成就'},
        {'name': '连续盈利', 'description': '连续30天盈利', 'category': '财务成就'},
        {'name': '财务大师', 'description': '累计盈利达到10亿元', 'category': '财务成就'},
        {'name': '投资专家', 'description': '单日盈利达到1000万元', 'category': '财务成就'},

        # 员工成就 (10个)
        {'name': '首位员工', 'description': '招聘第一名员工', 'category': '员工成就'},
        {'name': '十人团队', 'description': '拥有10名员工', 'category': '员工成就'},
        {'name': '百人团队', 'description': '拥有100名员工', 'category': '员工成就'},
        {'name': '千人团队', 'description': '拥有1000名员工', 'category': '员工成就'},
        {'name': '万人团队', 'description': '拥有10000名员工', 'category': '员工成就'},
        {'name': '人才伯乐', 'description': '招聘50名高级员工', 'category': '员工成就'},
        {'name': '培训大师', 'description': '完成100次员工培训', 'category': '员工成就'},
        {'name': '高薪一族', 'description': '员工平均工资超过1万元', 'category': '员工成就'},
        {'name': '人事专家', 'description': '员工满意度达到95%', 'category': '员工成就'},
        {'name': '团队建设', 'description': '所有部门都有员工', 'category': '员工成就'},

        # 发展成就 (10个)
        {'name': '星光初现', 'description': '酒店达到2星', 'category': '发展成就'},
        {'name': '三星荣耀', 'description': '酒店达到3星', 'category': '发展成就'},
        {'name': '四海为家', 'description': '酒店达到4星', 'category': '发展成就'},
        {'name': '五星级别', 'description': '酒店达到5星', 'category': '发展成就'},
        {'name': '六六大顺', 'description': '酒店达到6星', 'category': '发展成就'},
        {'name': '七星高照', 'description': '酒店达到7星', 'category': '发展成就'},
        {'name': '八方来客', 'description': '酒店达到8星', 'category': '发展成就'},
        {'name': '九霄云外', 'description': '酒店达到9星', 'category': '发展成就'},
        {'name': '部门齐全', 'description': '解锁所有部门', 'category': '发展成就'},
        {'name': '房间帝国', 'description': '拥有1000间房间', 'category': '发展成就'},

        # 经营成就 (10个)
        {'name': '客满为患', 'description': '入住率达到100%', 'category': '经营成就'},
        {'name': '满意服务', 'description': '客户满意度达到90分以上', 'category': '经营成就'},
        {'name': '声望卓著', 'description': '声望值达到10000', 'category': '经营成就'},
        {'name': '营销专家', 'description': '完成50次营销活动', 'category': '经营成就'},
        {'name': '广告大王', 'description': '营销投入累计达到1000万元', 'category': '经营成就'},
        {'name': '好评如潮', 'description': '连续100天满意度超过80分', 'category': '经营成就'},
        {'name': '生意兴隆', 'description': '连续30天入住率超过90%', 'category': '经营成就'},
        {'name': '稳定发展', 'description': '连续365天盈利', 'category': '经营成就'},
        {'name': '高端客户', 'description': '豪华房型入住率达到80%', 'category': '经营成就'},
        {'name': '品牌价值', 'description': '声望等级达到10级', 'category': '经营成就'},

        # 特殊成就 (10个)
        {'name': '时间管理大师', 'description': '使用时间加速功能100次', 'category': '特殊成就'},
        {'name': '存档专家', 'description': '游戏运行超过100小时', 'category': '特殊成就'},
        {'name': '探索者', 'description': '访问所有管理页面', 'category': '特殊成就'},
        {'name': '完美主义者', 'description': '所有指标都达到最高等级', 'category': '特殊成就'},
        {'name': '长期经营', 'description': '运营超过10年', 'category': '特殊成就'},
        {'name': '快速发展', 'description': '1年内达到5星', 'category': '特殊成就'},
        {'name': '节约大师', 'description': '单月支出控制在收入的50%以下', 'category': '特殊成就'},
        {'name': '平衡大师', 'description': '满意度、声望、盈利都达到优秀', 'category': '特殊成就'},
        {'name': '幸运之星', 'description': '连续7天都有好事发生', 'category': '特殊成就'},
        {'name': '挑战者', 'description': '在困难模式下达到5星', 'category': '特殊成就'},
    ]

    # 检查是否已经初始化过
    existing_count = Achievement.query.filter_by(hotel_id=hotel.id).count()
    if existing_count > 0:
        logger.info("成就已经初始化过，跳过")
        return

    # 创建成就记录
    for achievement_data in achievements_data:
        # 根据成就类型设置不同的奖励
        reward_money = 0
        reward_reputation = 100

        if achievement_data['category'] == '财务成就':
            reward_money = 50000  # 财务成就给金钱奖励
            reward_reputation = 200
        elif achievement_data['category'] == '员工成就':
            reward_money = 20000
            reward_reputation = 150
        elif achievement_data['category'] == '发展成就':
            reward_money = 100000  # 发展成就给更多金钱
            reward_reputation = 300
        elif achievement_data['category'] == '经营成就':
            reward_money = 30000
            reward_reputation = 250
        elif achievement_data['category'] == '特殊成就':
            reward_money = 200000  # 特殊成就给最多奖励
            reward_reputation = 500

        achievement = Achievement(
            hotel_id=hotel.id,
            name=achievement_data['name'],
            description=achievement_data['description'],
            category=achievement_data['category'],
            condition_type='custom',
            condition_value=0,
            reward_reputation=reward_reputation,
            reward_money=reward_money,
            achieved=False,
            reward_claimed=False
        )
        db.session.add(achievement)

    try:
        db.session.commit()
        logger.info(f"初始化了 {len(achievements_data)} 个成就")
    except Exception as e:
        db.session.rollback()
        logger.error(f"初始化成就失败: {e}")


def check_achievements(hotel):
    """检查并更新成就状态"""
    try:
        achievements = Achievement.query.filter_by(hotel_id=hotel.id, achieved=False).all()

        # 获取统计数据
        total_money = hotel.money
        total_employees = Employee.query.filter_by(hotel_id=hotel.id).count()
        total_rooms = sum(room.count for room in Room.query.filter_by(hotel_id=hotel.id).all())
        unlocked_departments = Department.query.filter_by(hotel_id=hotel.id, is_unlocked=True).count()

        newly_achieved = []

        for achievement in achievements:
            achieved = False

            # 财务成就
            if achievement.name == '初次盈利':
                achieved = hotel.money > 100000  # 资金超过10万
            elif achievement.name == '百万富翁':
                achieved = total_money >= 1000000
            elif achievement.name == '千万富翁':
                achieved = total_money >= 10000000
            elif achievement.name == '亿万富翁':
                achieved = total_money >= 100000000

            # 员工成就
            elif achievement.name == '首位员工':
                achieved = total_employees >= 1
            elif achievement.name == '百人团队':
                achieved = total_employees >= 100

            # 发展成就
            elif achievement.name == '星光初现':
                achieved = hotel.level >= 2
            elif achievement.name == '三星荣耀':
                achieved = hotel.level >= 3
            elif achievement.name == '五星级别':
                achieved = hotel.level >= 5
            elif achievement.name == '部门齐全':
                achieved = unlocked_departments >= 6  # 假设有6个主要部门

            # 经营成就
            elif achievement.name == '满意服务':
                achieved = hotel.satisfaction >= 90
            elif achievement.name == '声望卓著':
                achieved = hotel.reputation >= 10000
            elif achievement.name == '房间帝国':
                achieved = total_rooms >= 1000

            # 特殊成就
            elif achievement.name == '长期经营':
                achieved = hotel.days_elapsed >= 3650  # 10年
            elif achievement.name == '快速发展':
                achieved = hotel.level >= 5 and hotel.days_elapsed <= 365  # 1年内5星

            if achieved:
                achievement.achieved = True
                achievement.achieved_date = hotel.date
                newly_achieved.append(achievement)

        if newly_achieved:
            db.session.commit()
            logger.info(f"新解锁成就: {[a.name for a in newly_achieved]}")

        return newly_achieved

    except Exception as e:
        db.session.rollback()
        logger.error(f"检查成就时出错: {e}")
        return []


@bp.route('/get_achievements_list', methods=['GET'])
def get_achievements_list():
    """获取成就列表API"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        achievements = Achievement.query.filter_by(hotel_id=hotel.id).all()

        # 按分类组织成就
        achievement_groups = {
            '财务成就': [],
            '员工成就': [],
            '发展成就': [],
            '经营成就': [],
            '特殊成就': []
        }

        for achievement in achievements:
            achievement_data = {
                'id': achievement.id,
                'name': achievement.name,
                'description': achievement.description,
                'category': achievement.category,
                'condition_type': achievement.condition_type,
                'condition_value': achievement.condition_value,
                'reward_money': getattr(achievement, 'reward_money', 0),  # 金钱奖励
                'reward_reputation': getattr(achievement, 'reward_reputation', 0),  # 声望奖励
                'achieved': achievement.achieved,  # 保持一致的字段名
                'is_achieved': achievement.achieved,  # 兼容旧字段名
                'achieved_date': achievement.achieved_date.isoformat() if achievement.achieved_date else None,
                'achieved_at': achievement.achieved_date.isoformat() if achievement.achieved_date else None,  # 兼容旧字段名
                'progress': 100 if achievement.achieved else 0,
                'target': achievement.condition_value,
                'reward_claimed': getattr(achievement, 'reward_claimed', False)  # 奖励是否已领取
            }

            category = achievement.category if achievement.category in achievement_groups else '特殊成就'
            achievement_groups[category].append(achievement_data)

        # 统计成就完成情况
        total_achievements = len(achievements)
        achieved_count = sum(1 for a in achievements if a.achieved)  # 使用正确的字段名
        achievement_rate = (achieved_count / total_achievements * 100) if total_achievements > 0 else 0

        return jsonify({
            "success": True,
            "achievements": achievement_groups,
            "statistics": {
                "total": total_achievements,
                "achieved": achieved_count,
                "rate": round(achievement_rate, 1)
            }
        })
    except Exception as e:
        logger.error(f"获取成就列表时出错: {e}")
        return jsonify({"success": False, "message": "获取成就列表失败"}), 500


@bp.route('/claim', methods=['POST'])
def claim_achievement():
    """领取成就奖励"""
    try:
        data = request.get_json()
        achievement_id = data.get('achievement_id')

        if not achievement_id:
            return jsonify({"success": False, "message": "缺少成就ID"}), 400

        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        # 尝试按ID查找，如果失败则按name查找
        achievement = Achievement.query.filter_by(hotel_id=hotel.id, id=achievement_id).first()
        if not achievement:
            achievement = Achievement.query.filter_by(hotel_id=hotel.id, name=achievement_id).first()

        if not achievement:
            return jsonify({"success": False, "message": "成就不存在"}), 404

        if not achievement.achieved:
            return jsonify({"success": False, "message": "成就尚未完成"}), 400

        if achievement.reward_claimed:
            return jsonify({"success": False, "message": "奖励已经领取"}), 400

        # 发放奖励
        if achievement.reward_money > 0:
            hotel.money += achievement.reward_money

        if achievement.reward_reputation > 0:
            hotel.reputation += achievement.reward_reputation

        achievement.reward_claimed = True
        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"成功领取成就奖励：¥{achievement.reward_money:,}，+{achievement.reward_reputation}声望"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"领取成就奖励时出错: {e}")
        return jsonify({"success": False, "message": "领取奖励失败"}), 500
