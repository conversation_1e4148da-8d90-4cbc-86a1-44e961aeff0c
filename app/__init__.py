from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import Config
import logging

# 设置日志编码为UTF-8，解决中文乱码问题
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]',
    handlers=[
        logging.StreamHandler()  # 只输出到控制台，不写入文件
    ]
)
logger = logging.getLogger(__name__)

db = SQLAlchemy()


def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    db.init_app(app)
    
    # 注册蓝图
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.hotel import bp as hotel_bp
    app.register_blueprint(hotel_bp, url_prefix='/hotel')
    
    from app.rooms import bp as rooms_bp
    app.register_blueprint(rooms_bp, url_prefix='/rooms')
    
    from app.employees import bp as employees_bp
    app.register_blueprint(employees_bp, url_prefix='/employees')
    
    from app.departments import bp as departments_bp
    app.register_blueprint(departments_bp, url_prefix='/departments')
    
    from app.finance import bp as finance_bp
    app.register_blueprint(finance_bp, url_prefix='/finance')
    
    from app.achievements import bp as achievements_bp
    app.register_blueprint(achievements_bp, url_prefix='/achievements')
    
    from app.marketing import bp as marketing_bp
    app.register_blueprint(marketing_bp, url_prefix='/marketing')

    from app.events import bp as events_bp
    app.register_blueprint(events_bp, url_prefix='/events')

    from app.save import bp as save_bp
    app.register_blueprint(save_bp, url_prefix='/save')

    logger.info("酒店管理系统启动")
    
    
    return app