from flask import render_template, request, jsonify
from app import db
from app.models import Hotel, Room, FinancialRecord, GameSetting
from app.main.utils import get_current_hotel
import logging

logger = logging.getLogger(__name__)

from app.rooms import bp


@bp.route('/management')
def management():
    """房间管理页面"""
    hotel = get_current_hotel()
    if not hotel:
        return "酒店数据未初始化", 500

    # 获取所有房间
    rooms = get_sorted_rooms(hotel.id)

    # 定义房间价格
    room_prices = {
        "单人间": 300,
        "标准间": 500,
        "大床房": 700,
        "家庭房": 1000,
        "商务间": 1500,
        "行政间": 2000,
        "豪华间": 3000,
        "总统套房": 5000,
        "皇家套房": 8000,
        "总统别墅": 15000,
        "皇宫套房": 30000
    }

    # 定义房间建设费用（房间价格的10倍）
    room_build_costs = {}
    for room_type, price in room_prices.items():
        room_build_costs[room_type] = price * 10

    # 定义房间解锁要求（统一标准）
    room_requirements = {
        "单人间": 1,
        "标准间": 1,
        "大床房": 2,
        "家庭房": 2,
        "商务间": 3,
        "行政间": 4,
        "豪华间": 5,
        "总统套房": 6,
        "皇家套房": 7,
        "总统别墅": 8,
        "皇宫套房": 9
    }

    # 按房间类型分组
    rooms_by_type = {}
    for room_type in room_prices.keys():
        rooms_by_type[room_type] = [room for room in rooms if room.type == room_type]

    # 计算统计数据
    total_rooms = sum(room.count for room in rooms)

    # 计算真实的平均入住率
    if total_rooms > 0:
        from app.main.utils import calculate_stable_occupancy_rate
        total_occupancy = 0
        total_weighted_rooms = 0

        for room in rooms:
            if room.count > 0:
                room_occupancy = calculate_stable_occupancy_rate(hotel, room.type, hotel.date)
                total_occupancy += room_occupancy * room.count
                total_weighted_rooms += room.count

        occupancy_rate = total_occupancy / total_weighted_rooms if total_weighted_rooms > 0 else 0
        occupied_rooms = int(total_rooms * occupancy_rate / 100)
    else:
        occupancy_rate = 0
        occupied_rooms = 0

    monthly_maintenance = total_rooms * 100  # 每间房每月100元维护费

    # 计算各房型入住率（使用实际计算方法）
    room_occupancy_rates = {}
    for room_type in room_prices.keys():
        # 使用与总入住率相同的计算方法
        if hotel:
            room_occupancy_rates[room_type] = calculate_stable_occupancy_rate(hotel, room_type, hotel.date)
        else:
            room_occupancy_rates[room_type] = 70.0

    return render_template('rooms.html',
                         hotel=hotel,
                         rooms=rooms,
                         rooms_by_type=rooms_by_type,
                         room_prices=room_prices,
                         room_build_costs=room_build_costs,
                         room_requirements=room_requirements,
                         room_occupancy_rates=room_occupancy_rates,
                         total_rooms=total_rooms,
                         occupied_rooms=occupied_rooms,
                         occupancy_rate=occupancy_rate,
                         monthly_maintenance=monthly_maintenance)


@bp.route('/build', methods=['POST'])
def build_room():
    """建设房间"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')
        quantity = data.get('quantity', 1)

        if not room_type:
            return jsonify({"success": False, "message": "房间类型不能为空"}), 400

        if not isinstance(quantity, int) or quantity <= 0:
            return jsonify({"success": False, "message": "房间数量必须是正整数"}), 400

        # 定义房间价格
        room_prices = {
            "单人间": 300, "标准间": 500, "大床房": 700, "家庭房": 1000,
            "商务间": 1500, "行政间": 2000, "豪华间": 3000, "总统套房": 5000,
            "皇家套房": 8000, "总统别墅": 15000, "皇宫套房": 30000
        }

        # 定义房间解锁要求
        room_requirements = {
            "单人间": 1, "标准间": 1, "大床房": 2, "家庭房": 2,
            "商务间": 3, "行政间": 3, "豪华间": 4, "总统套房": 5,
            "皇家套房": 6, "总统别墅": 7, "皇宫套房": 8
        }

        # 检查酒店等级是否满足要求
        required_level = room_requirements.get(room_type, 1)
        if hotel.level < required_level:
            return jsonify({"success": False, "message": f"需要{required_level}星酒店才能建设{room_type}"}), 400

        # 计算建设费用
        room_price = room_prices.get(room_type, 500)
        construction_cost = room_price * 10  # 建设成本是房间价格的10倍
        total_cost = construction_cost * quantity

        # 检查资金是否足够
        if hotel.money < total_cost:
            return jsonify({"success": False, "message": f"资金不足，需要¥{total_cost:,}"}), 400

        # 查找或创建房间记录
        room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if not room:
            room = Room(
                hotel_id=hotel.id,
                type=room_type,
                count=0,
                price=room_price
            )
            db.session.add(room)

        # 更新房间数量
        room.count += quantity

        # 扣除费用
        hotel.money -= total_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=total_cost,
            description=f"建设 {quantity} 间 {room_type}"
        )
        db.session.add(financial_record)

        # 提交数据库更改
        try:
            db.session.commit()
            logger.info(f"成功建设 {quantity} 间 {room_type}，当前总数: {room.count}")
            return jsonify({"success": True, "message": f"成功建设 {quantity} 间 {room_type}，花费¥{total_cost:,}"})
        except Exception as commit_error:
            db.session.rollback()
            logger.error(f"提交房间建设时出错: {commit_error}")
            return jsonify({"success": False, "message": "建设房间失败，请重试"}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"建设房间时出错: {e}")
        return jsonify({"success": False, "message": "建设房间失败"}), 500


@bp.route('/add', methods=['POST'])
def add_rooms():
    """新增房间"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')
        quantity = int(data.get('quantity', 1))

        if not room_type or quantity < 1:
            return jsonify({"success": False, "message": "请提供有效的房间类型和数量"}), 400

        # 检查房型是否已解锁
        room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if not room:
            return jsonify({"success": False, "message": f"{room_type}尚未解锁"}), 400

        # 计算建设费用（房间单价 × 10 × 数量）
        room_prices = {
            '单人间': 200, '标准间': 300, '大床房': 400, '家庭房': 500,
            '商务间': 600, '行政间': 800, '豪华间': 1000, '总统套房': 2000,
            '皇家套房': 3000, '总统别墅': 5000, '皇宫套房': 8000
        }

        room_price = room_prices.get(room_type, 300)
        build_cost = room_price * 10 * quantity

        # 检查资金是否足够
        if hotel.money < build_cost:
            return jsonify({"success": False, "message": f"资金不足，需要¥{build_cost:,}"}), 400

        # 增加房间数量
        room.count += quantity

        # 扣除建设费用
        hotel.money -= build_cost

        # 记录财务记录
        from app.models import FinancialRecord
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=build_cost,
            description=f"新增{quantity}间{room_type}"
        )
        db.session.add(financial_record)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"成功新增{quantity}间{room_type}！建设费用¥{build_cost:,}"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"新增房间时出错: {e}")
        return jsonify({"success": False, "message": "新增房间失败"}), 500


@bp.route('/unlock', methods=['POST'])
def unlock_room_type():
    """解锁房间类型"""
    try:
        from app.models import Room, FinancialRecord  # 在函数开始就导入所需模型

        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')

        if not room_type:
            return jsonify({"success": False, "message": "请提供房间类型"}), 400

        # 检查房型是否已存在
        existing_room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if existing_room:
            return jsonify({"success": False, "message": f"{room_type}已解锁"}), 400

        # 检查酒店等级要求（与页面显示保持一致）
        room_requirements = {
            '单人间': 1, '标准间': 1, '大床房': 2, '家庭房': 2,
            '商务间': 3, '行政间': 4, '豪华间': 5, '总统套房': 6,
            '皇家套房': 7, '总统别墅': 8, '皇宫套房': 9
        }

        required_level = room_requirements.get(room_type, 1)
        if hotel.level < required_level:
            return jsonify({"success": False, "message": f"需要{required_level}星酒店才能解锁{room_type}"}), 400

        # 创建新房型（初始1间）
        room_prices = {
            '单人间': 200, '标准间': 300, '大床房': 400, '家庭房': 500,
            '商务间': 600, '行政间': 800, '豪华间': 1000, '总统套房': 2000,
            '皇家套房': 3000, '总统别墅': 5000, '皇宫套房': 8000
        }

        room_price = room_prices.get(room_type, 300)
        unlock_cost = room_price * 10  # 解锁费用 = 建设1间的费用

        # 检查资金
        if hotel.money < unlock_cost:
            return jsonify({"success": False, "message": f"资金不足，需要¥{unlock_cost:,}"}), 400

        new_room = Room(
            hotel_id=hotel.id,
            type=room_type,
            count=1,  # 解锁时自动建设1间
            price=room_price
        )
        db.session.add(new_room)

        # 扣除解锁费用
        hotel.money -= unlock_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            income=0,
            expense=unlock_cost,
            description=f"解锁{room_type}并建设1间"
        )
        db.session.add(financial_record)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"成功解锁{room_type}并建设1间！费用¥{unlock_cost:,}"
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"解锁房型时出错: {e}")
        return jsonify({"success": False, "message": "解锁房型失败"}), 500


@bp.route('/add_room', methods=['POST'])
def add_room():
    """添加房间"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')
        count = data.get('count', 1)

        if not room_type:
            return jsonify({"success": False, "message": "房间类型不能为空"}), 400

        if not isinstance(count, int) or count <= 0:
            return jsonify({"success": False, "message": "房间数量必须是正整数"}), 400

        # 检查房间类型是否已解锁
        room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if not room:
            return jsonify({"success": False, "message": f"请先解锁{room_type}"}), 400

        # 计算建设费用（根据优化需求文档：房间价格的10倍作为建设成本）
        room_prices = {
            "单人间": 300,
            "标准间": 500,
            "大床房": 700,
            "家庭房": 1000,
            "商务间": 1500,
            "行政间": 2000,
            "豪华间": 3000,
            "总统套房": 5000,
            "皇家套房": 8000,
            "总统别墅": 15000,
            "皇宫套房": 30000
        }

        room_price = room_prices.get(room_type, 500)
        room_construction_cost = room_price * 10  # 建设成本是房间价格的10倍
        total_cost = room_construction_cost * count

        # 检查资金是否足够
        if hotel.money < total_cost:
            return jsonify({"success": False, "message": "资金不足"}), 400

        # 更新房间数量
        room.count += count

        # 扣除费用
        hotel.money -= total_cost

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=total_cost,
            description=f"购买 {count} 间 {room_type}"
        )
        db.session.add(financial_record)

        db.session.commit()

        return jsonify({"success": True, "message": f"成功添加 {count} 间 {room_type}"})
    except Exception as e:
        db.session.rollback()
        logger.error(f"添加房间时出错: {e}")
        return jsonify({"success": False, "message": "添加房间失败"}), 500





@bp.route('/set_price', methods=['POST'])
def set_room_price():
    """设置房间价格"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        data = request.get_json()
        room_type = data.get('room_type')
        new_price = data.get('price')

        if not room_type:
            return jsonify({"success": False, "message": "房间类型不能为空"}), 400

        if not isinstance(new_price, (int, float)) or new_price <= 0:
            return jsonify({"success": False, "message": "价格必须是正数"}), 400

        # 查找房间
        room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
        if not room:
            return jsonify({"success": False, "message": f"{room_type}不存在"}), 400

        # 更新价格
        old_price = room.price
        room.price = int(new_price)

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=0,  # 价格调整不产生费用
            description=f"{room_type}价格调整: ¥{old_price} → ¥{new_price}"
        )
        db.session.add(financial_record)

        db.session.commit()

        return jsonify({
            "success": True,
            "message": f"{room_type}价格已更新为¥{new_price}"
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"设置房间价格时出错: {e}")
        return jsonify({"success": False, "message": "设置价格失败"}), 500


def get_sorted_rooms(hotel_id):
    """获取按类型排序的房间列表"""
    # 定义房间类型的顺序（按照解锁顺序）
    room_type_order = {
        "单人间": 1,
        "标准间": 2,
        "家庭房": 3,
        "商务间": 4,
        "行政间": 5,
        "豪华间": 6,
        "总统套房": 7,
        "皇家套房": 8,
        "总统别墅": 9,
        "皇宫套房": 10
    }
    
    rooms = Room.query.filter_by(hotel_id=hotel_id).all()
    
    # 按照预定义顺序排序
    rooms.sort(key=lambda r: room_type_order.get(r.type, 999))
    return rooms


def get_default_room_price(room_type):
    """获取默认房间价格"""
    # 定义房间价格
    room_prices = {
        "单人间": 300,
        "标准间": 500,
        "家庭房": 700,
        "商务间": 1000,
        "海景房": 1500,
        "豪华间": 2000,
        "家庭套房": 5000,
        "商务套房": 8000,
        "豪华套房": 15000,
        "总统套房": 30000,
        "皇家套房": 8000,
        "总统别墅": 15000,
        "皇宫套房": 30000
    }
    return room_prices.get(room_type, 300)


def get_room_unlock_requirements(hotel_level):
    """获取各等级酒店可解锁的房间类型"""
    # 定义房间解锁规则（星级酒店对应解锁的房间类型）
    room_unlock_rules = {
        1: ["单人间", "标准间"],
        2: ["家庭房"],
        3: ["商务间"],
        4: ["海景房"],
        5: ["豪华间"],
        6: ["家庭套房"],
        7: ["商务套房"],
        8: ["豪华套房"],
        9: ["总统套房"]
    }
    
    # 收集当前等级及以下所有可解锁的房间类型
    unlocked_rooms = []
    for level in range(1, hotel_level + 1):
        if level in room_unlock_rules:
            unlocked_rooms.extend(room_unlock_rules[level])
    
    return unlocked_rooms


def calculate_room_price(room_type):
    """计算房间价格"""
    # 获取数据库中的价格设置
    price_setting = GameSetting.query.filter_by(key=f'room_price_{room_type}').first()
    if price_setting:
        try:
            return int(price_setting.value)
        except (ValueError, TypeError):
            return get_default_room_price(room_type)
    return get_default_room_price(room_type)


def get_unlockable_rooms(hotel):
    """获取当前可解锁的房间类型"""
    # 定义房间解锁规则（星级酒店对应解锁的房间类型）
    room_unlock_rules = {
        1: ["单人间", "标准间"],
        2: ["家庭房"],
        3: ["商务间"],
        4: ["海景房"],
        5: ["豪华间"],
        6: ["家庭套房"],
        7: ["商务套房"],
        8: ["豪华套房"],
        9: ["总统套房"]
    }
    
    unlockable = []
    if hotel.level in room_unlock_rules:
        for room_type in room_unlock_rules[hotel.level]:
            # 检查是否已拥有该类型房间
            existing_room = Room.query.filter_by(hotel_id=hotel.id, type=room_type).first()
            if not existing_room:
                unlockable.append({
                    "type": room_type,
                    "cost": 10000 * hotel.level  # 解锁费用为10000*酒店等级
                })
    
    return unlockable


def check_room_unlock_conditions(hotel, room_type):
    """检查房间解锁条件"""
    # 定义房间解锁规则（星级酒店对应解锁的房间类型）
    room_unlock_rules = {
        1: ["单人间", "标准间"],
        2: ["家庭房"],
        3: ["商务间"],
        4: ["海景房"],
        5: ["豪华间"],
        6: ["家庭套房"],
        7: ["商务套房"],
        8: ["豪华套房"],
        9: ["总统套房"]
    }
    
    # 检查当前酒店等级是否可以解锁该房间类型
    if hotel.level not in room_unlock_rules or room_type not in room_unlock_rules[hotel.level]:
        return False, '该房间类型暂不可解锁'
    
    # 检查资金是否足够
    # 房间解锁费用规则：1星1万，2星2万，依此类推
    unlock_cost = 10000 * hotel.level
    if hotel.money < unlock_cost:
        return False, '资金不足'
    
    return True, unlock_cost  # 返回True和费用而不是只返回True


@bp.route('/get_rooms_list', methods=['GET'])
def get_rooms_list():
    """获取房间列表API"""
    try:
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({"success": False, "message": "酒店数据未初始化"}), 500

        rooms = Room.query.filter_by(hotel_id=hotel.id).all()

        # 定义房间解锁要求
        room_requirements = {
            "单人间": 1, "标准间": 1, "大床房": 2, "家庭房": 3,
            "商务间": 4, "行政间": 5, "豪华间": 6, "总统套房": 7,
            "皇家套房": 8, "总统别墅": 9, "皇宫套房": 9
        }

        rooms_data = []
        for room in rooms:
            level_required = room_requirements.get(room.type, 1)
            is_unlocked = hotel.level >= level_required

            rooms_data.append({
                'id': room.id,
                'type': room.type,
                'count': room.count,
                'price': room.price,
                'level_required': level_required,
                'is_unlocked': is_unlocked,
                'occupancy_rate': calculate_room_occupancy_rate(room)
            })

        return jsonify({
            "success": True,
            "rooms": rooms_data
        })
    except Exception as e:
        logger.error(f"获取房间列表时出错: {e}")
        return jsonify({"success": False, "message": "获取房间列表失败"}), 500


def calculate_room_occupancy_rate(room):
    """计算房间入住率"""
    try:
        if room.count == 0:
            return 0.0

        # 获取酒店信息
        from app.main.utils import get_current_hotel, calculate_stable_occupancy_rate
        hotel = get_current_hotel()

        if hotel:
            # 使用稳定的入住率计算
            return calculate_stable_occupancy_rate(hotel, room.type, hotel.date)
        else:
            # 备用计算方式
            base_rates = {
                '单人间': 75, '标准间': 80, '大床房': 78, '家庭房': 72,
                '商务间': 85, '行政间': 88, '豪华间': 82, '总统套房': 75,
                '皇家套房': 70, '总统别墅': 65, '皇宫套房': 60
            }
            return base_rates.get(room.type, 70)
    except Exception as e:
        logger.error(f"计算房间入住率时出错: {e}")
        return 70.0
