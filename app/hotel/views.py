from flask import render_template, request, jsonify
from app import db
from app.models import Hotel, Room, Employee, Department, FinancialRecord
from . import bp
import logging

logger = logging.getLogger(__name__)

# 星级升级配置（根据优化需求文档修正）
UPGRADE_REQUIREMENTS = {
    1: {"cost": 0, "reputation": 0, "days": 0, "satisfaction": 0, "description": "1星酒店"},
    2: {"cost": 1000000, "reputation": 500, "days": 30, "satisfaction": 60, "description": "2星酒店"},
    3: {"cost": 3000000, "reputation": 1500, "days": 90, "satisfaction": 65, "description": "3星酒店"},
    4: {"cost": 8000000, "reputation": 3000, "days": 180, "satisfaction": 70, "description": "4星酒店"},
    5: {"cost": 20000000, "reputation": 6000, "days": 365, "satisfaction": 75, "description": "5星酒店"},
    6: {"cost": 50000000, "reputation": 12000, "days": 730, "satisfaction": 80, "description": "6星酒店"},
    7: {"cost": 100000000, "reputation": 25000, "days": 1095, "satisfaction": 85, "description": "7星酒店"},
    8: {"cost": 200000000, "reputation": 50000, "days": 1460, "satisfaction": 90, "description": "8星酒店"},
    9: {"cost": 500000000, "reputation": 100000, "days": 1825, "satisfaction": 95, "description": "9星酒店"}
}

UNLOCKED_DEPARTMENTS = {
    1: ["前台部", "客房部", "人事部"],  # 1星酒店默认解锁人事部
    2: ["营销部"],
    3: ["餐饮部"],
    4: ["安保部"],
    5: ["财务部"],
    6: ["商务部"],
    7: ["工程部"],
    8: ["康养部"],
    9: ["董事会"]
}

# 新增房间类型（根据优化需求文档修正）
NEW_ROOM_TYPES = {
    1: ["单人间", "标准间"],
    2: ["大床房"],
    3: ["家庭房"],
    4: ["商务间"],
    5: ["行政间"],
    6: ["豪华间"],
    7: ["总统套房"],
    8: ["皇家套房"],
    9: ["总统别墅", "皇宫套房"]
}

def calculate_upgrade_cost(hotel):
    """计算升级费用"""
    next_level = hotel.level + 1
    if next_level in UPGRADE_REQUIREMENTS:
        return UPGRADE_REQUIREMENTS[next_level]["cost"]
    return next_level * 500000

def check_upgrade_conditions(hotel):
    """检查升级条件（根据优化需求文档修正）"""
    next_level = hotel.level + 1
    if next_level not in UPGRADE_REQUIREMENTS:
        return False, '无法升级到更高星级'

    requirements = UPGRADE_REQUIREMENTS[next_level]
    upgrade_cost = requirements["cost"]
    required_reputation = requirements["reputation"]
    required_days = requirements["days"]
    required_satisfaction = requirements["satisfaction"]

    # 检查资金是否足够
    if hotel.money < upgrade_cost:
        return False, f'资金不足（需要{upgrade_cost:,}元）'

    # 检查声望是否足够
    if hotel.reputation < required_reputation:
        return False, f'声望不足（需要{required_reputation:,}点声望）'

    # 检查运营天数是否足够
    if hotel.days_elapsed < required_days:
        return False, f'运营天数不足（需要{required_days}天，当前{hotel.days_elapsed}天）'

    # 检查客户满意度是否足够
    if hotel.satisfaction < required_satisfaction:
        return False, f'客户满意度不足（需要{required_satisfaction}分，当前{hotel.satisfaction:.1f}分）'
    
    # 检查当前等级需要解锁的部门是否已解锁
    required_departments = UNLOCKED_DEPARTMENTS.get(next_level, [])
    for dept_name in required_departments:
        dept = Department.query.filter_by(hotel_id=hotel.id, name=dept_name).first()
        if not dept or not dept.is_unlocked:
            return False, f'请先解锁{dept_name}'
    
    return True, {"cost": upgrade_cost, "reputation": required_reputation}

@bp.route('/management')
def management():
    """酒店管理页面"""
    from app.main.utils import get_current_hotel
    hotel = get_current_hotel()
    if not hotel:
        return "酒店数据未初始化", 500

    # 构建升级要求数据
    upgrade_requirements = {}
    can_upgrade = {}

    for level in range(1, 10):  # 从1星开始，显示所有等级
        requirements = UPGRADE_REQUIREMENTS[level]
        upgrade_requirements[level] = requirements

        # 检查是否可以升级到这个等级
        if level == hotel.level + 1:  # 只能升级到下一个等级
            can_upgrade_to_level = (
                hotel.money >= requirements["cost"] and
                hotel.reputation >= requirements["reputation"] and
                hotel.days_elapsed >= requirements["days"] and
                hotel.satisfaction >= requirements["satisfaction"]
            )
            can_upgrade[level] = can_upgrade_to_level
        else:
            can_upgrade[level] = False

    return render_template('upgrade.html',
                         hotel=hotel,
                         upgrade_requirements=upgrade_requirements,
                         can_upgrade=can_upgrade)

@bp.route('/upgrade', methods=['POST'])
def upgrade():
    """酒店升级"""
    try:
        from app.main.utils import get_current_hotel
        hotel = get_current_hotel()
        if not hotel:
            return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

        data = request.get_json()
        target_level = data.get('level')

        if not target_level or target_level != hotel.level + 1:
            return jsonify({'success': False, 'message': '只能升级到下一个等级'}), 400

        if target_level not in UPGRADE_REQUIREMENTS:
            return jsonify({'success': False, 'message': '无效的目标等级'}), 400

        requirements = UPGRADE_REQUIREMENTS[target_level]

        # 检查升级条件
        if hotel.money < requirements["cost"]:
            return jsonify({'success': False, 'message': f'资金不足，需要¥{requirements["cost"]:,}'}), 400

        if hotel.reputation < requirements["reputation"]:
            return jsonify({'success': False, 'message': f'声望不足，需要{requirements["reputation"]}点'}), 400

        if hotel.days_elapsed < requirements["days"]:
            return jsonify({'success': False, 'message': f'运营天数不足，需要{requirements["days"]}天'}), 400

        if hotel.satisfaction < requirements["satisfaction"]:
            return jsonify({'success': False, 'message': f'满意度不足，需要{requirements["satisfaction"]}分'}), 400

        # 执行升级
        old_level = hotel.level
        hotel.level = target_level
        hotel.money -= requirements["cost"]
        hotel.reputation += target_level * 100  # 升级奖励声望

        # 部门需要在部门管理中手动解锁，不再自动解锁
        new_departments = UNLOCKED_DEPARTMENTS.get(target_level, [])

        # 记录财务记录
        financial_record = FinancialRecord(
            hotel_id=hotel.id,
            record_date=hotel.date,
            expense=requirements["cost"],
            description=f"酒店从{old_level}星升级到{target_level}星"
        )
        db.session.add(financial_record)

        # 使用安全的数据库操作
        from app.main.utils import safe_database_operation

        def commit_operation():
            db.session.commit()
            return True

        result = safe_database_operation(commit_operation)
        if result:
            return jsonify({
                'success': True,
                'message': f'恭喜！酒店成功升级到{target_level}星！获得声望奖励{target_level * 100}点，可在部门管理中解锁新部门：{", ".join(new_departments)}',
                'new_level': hotel.level,
                'new_money': hotel.money,
                'new_reputation': hotel.reputation
            })
        else:
            return jsonify({'success': False, 'message': '升级失败，请重试'}), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"酒店升级时出错: {e}")
        return jsonify({'success': False, 'message': '升级失败'}), 500


@bp.route('/upgrade/<int:target_level>', methods=['POST'])
def upgrade_to_level(target_level):
    """升级酒店到指定星级"""
    hotel = Hotel.query.first()
    if not hotel:
        return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500

    # 验证目标星级
    if target_level < 1 or target_level > 9:
        return jsonify({'success': False, 'message': '目标星级必须在1-9之间'}), 400

    # 检查是否已经达到了目标星级或更高星级
    if hotel.level >= target_level:
        return jsonify({'success': False, 'message': f'酒店已经是{hotel.level}星或更高星级'}), 400

    # 检查是否可以升级到目标星级（必须逐级升级）
    if target_level != hotel.level + 1:
        return jsonify({'success': False, 'message': f'必须先升星到{hotel.level + 1}星'}), 400

    # 获取升级要求
    if target_level in UPGRADE_REQUIREMENTS:
        upgrade_cost = UPGRADE_REQUIREMENTS[target_level]["cost"]
        required_reputation = UPGRADE_REQUIREMENTS[target_level]["reputation"]
    else:
        upgrade_cost = target_level * 500000  # 默认计算方法
        required_reputation = target_level * 5000

    # 检查资金是否足够
    if hotel.money < upgrade_cost:
        return jsonify({'success': False, 'message': '资金不足'}), 400

    # 检查声望是否足够
    if hotel.reputation < required_reputation:
        return jsonify({'success': False, 'message': f'声望不足（需要{required_reputation}点声望）'}), 400

    # 执行升级
    old_level = hotel.level
    hotel.level = target_level
    hotel.money -= upgrade_cost

    # 记录财务记录
    financial_record = FinancialRecord(
        hotel_id=hotel.id,
        record_date=hotel.date,
        expense=upgrade_cost,
        description=f"酒店从{old_level}星升星到{target_level}星"
    )
    db.session.add(financial_record)

    try:
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'酒店成功从{old_level}星升星到{target_level}星',
            'new_level': hotel.level,
            'new_balance': hotel.money
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"酒店升级时出错: {e}")
        return jsonify({'success': False, 'message': '升级失败'}), 500


@bp.route('/update_hotel_name', methods=['POST'])
def update_hotel_name():
    """更新酒店名称"""
    try:
        hotel = Hotel.query.first()
        if not hotel:
            return jsonify({'success': False, 'message': '酒店数据未初始化'}), 500
            
        data = request.get_json()
        new_name = data.get('name', '').strip()
        
        if not new_name:
            return jsonify({'success': False, 'message': '酒店名称不能为空'}), 400
            
        old_name = hotel.name
        hotel.name = new_name
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'酒店名称已从"{old_name}"更新为"{new_name}"'
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新酒店名称时出错: {e}")
        return jsonify({'success': False, 'message': '更新酒店名称失败'}), 500