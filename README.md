# 酒店管理系统

一个基于Web的模拟经营游戏，让玩家体验从经营小型酒店到发展为豪华酒店集团的全过程。

## 项目概述

酒店管理系统是一个单机版的Web应用程序，玩家可以在本地计算机上运行，无需网络连接即可体验完整的酒店经营流程。系统提供了丰富的功能模块，包括员工管理、部门管理、房间管理、营销管理、财务管理等，让玩家能够全面体验酒店经营的各个方面。

## 功能特性

### 核心功能
- **酒店基本信息管理** - 显示和修改酒店名称、等级、资金等基本信息
- **时间管理系统** - 自动时间推进和手动时间控制
- **员工管理系统** - 员工招聘、解雇、等级晋升和工资管理
- **部门管理系统** - 部门解锁和管理
- **房间管理系统** - 房间类型解锁和数量管理
- **营销管理系统** - 广告投放和活动举办
- **财务管理系统** - 收支记录和财务统计
- **游戏存档系统** - 多存档位管理和读档功能

### 游戏机制
- **等级升级系统** - 通过满足条件逐步提升酒店等级
- **动态经营环境** - 时间推进带来收入、支出等变化
- **数据可视化** - 图表展示经营状况，辅助决策
- **规则系统** - 基于数据库的可配置游戏规则

## 技术架构

### 后端技术
- **编程语言**: Python 3.7+
- **Web框架**: Flask
- **数据库**: SQLite
- **ORM**: Flask-SQLAlchemy

### 前端技术
- **HTML5/CSS3** - 页面结构和样式
- **JavaScript** - 交互功能
- **Bootstrap 5** - 响应式布局
- **Chart.js** - 数据可视化

### 项目结构
```
hotel-management-system/
├── app/                    # 应用主目录
│   ├── __init__.py         # 应用初始化
│   ├── models.py           # 数据模型定义
│   ├── main/               # 主模块
│   ├── employees/          # 员工管理模块
│   ├── departments/        # 部门管理模块
│   ├── rooms/              # 房间管理模块
│   ├── hotel/              # 酒店管理模块
│   ├── marketing/          # 营销管理模块
│   ├── finance/            # 财务管理模块
│   ├── templates/          # HTML模板文件
│   └── static/             # 静态资源文件
├── config.py               # 配置文件
├── app.db                  # SQLite数据库文件
├── run.py                  # 应用启动文件
└── requirements.txt        # 依赖包列表
```

## 快速开始

### 环境要求
- Python 3.7或更高版本
- Windows 7/8/10/11, macOS 10.12+ 或 Linux

### 安装步骤

1. 克隆或下载项目代码：
```bash
git clone <项目地址>
cd hotel-management-system
```

2. （推荐）创建虚拟环境：
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 启动应用：
```bash
python run.py
```

5. 在浏览器中访问：
```
http://127.0.0.1:5000
```

## 使用说明

### 首页
首页是游戏的主界面，展示了酒店的基本信息和经营状况：
- 酒店名称、等级、资金、日期等基本信息
- 各部门繁忙程度和收入的可视化图表
- 财务记录列表
- 时间控制按钮

### 员工管理
在员工管理页面可以：
- 查看各部门员工列表
- 招聘新员工
- 解雇现有员工
- 批量操作员工

### 部门管理
在部门管理页面可以：
- 查看各部门解锁状态
- 解锁满足条件的新部门
- 了解各部门解锁所需条件

### 房间管理
在房间管理页面可以：
- 查看各房间类型和数量
- 解锁新的房间类型
- 添加房间数量

### 酒店升级
在酒店升级页面可以：
- 查看当前酒店等级
- 了解升级到各等级的条件和费用
- 执行酒店升级操作

### 营销管理
在营销管理页面可以：
- 投放不同类型的广告
- 举办各种营销活动
- 查看营销效果

### 财务管理
在财务管理页面可以：
- 查看详细的财务记录
- 了解收入和支出的分类统计
- 分析酒店的财务状况

### 游戏存档
在首页可以：
- 保存当前游戏进度到不同存档位
- 从存档位加载游戏进度
- 开始新的游戏

## 文档资料

- [需求文档](REQUIREMENTS.md) - 详细描述系统功能和非功能需求
- [设计文档](DESIGN.md) - 系统架构设计和技术实现细节
- [部署文档](DEPLOYMENT.md) - 系统安装、配置和维护指南

## 开发相关

### 脚本工具
项目包含一些管理脚本，位于[scripts](scripts)目录中：
- [check_game_rules.py](scripts/check_game_rules.py) - 检查数据库中的游戏规则
- [init_game_rules.py](scripts/init_game_rules.py) - 初始化数据库中的游戏规则

### 代码规范
- 遵循Python PEP8代码规范
- HTML模板与JavaScript、CSS分离
- 数据库操作使用ORM，避免直接SQL
- 业务规则存储在数据库中，而非硬编码

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目仅供学习和参考使用。

## 联系方式

如有问题，请提交Issue或联系项目维护者。