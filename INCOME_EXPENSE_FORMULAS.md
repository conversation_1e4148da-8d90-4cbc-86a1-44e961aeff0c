# 💰 酒店管理系统收入支出计算公式文档

## 📊 收入计算公式

### 1. 房间收入（主要收入来源）
**计算公式**：
```
房间收入 = Σ(房间数量 × 房间单价 × 入住率)
```

**详细说明**：
- **房间数量**：每种房型的房间总数
- **房间单价**：每间房每晚的价格（元/晚）
- **入住率**：四舍五入到整数百分比后计算

**入住率计算**：
```
基础入住率 = 房间类型基础率 + 酒店等级加成 + 满意度影响 + 声望影响 + 营销影响
入住率（最终） = round(基础入住率) / 100
```

**房间类型基础入住率**：
- 单人间：75%
- 标准间：80%
- 大床房：78%
- 家庭房：72%
- 商务间：85%
- 行政间：88%
- 豪华间：82%
- 总统套房：75%
- 皇家套房：70%
- 总统别墅：65%
- 皇宫套房：60%

**影响因素**：
- 酒店等级加成：每级+2%
- 满意度影响：(满意度-50) × 0.1%
- 声望影响：声望等级 × 1%
- 营销活动影响：活跃营销活动效果累加

### 2. 服务收入（附加收入）
**计算公式**：
```
服务收入 = 房间收入 × 10%
```

**说明**：包括餐饮、洗衣、客房服务等附加服务收入

### 3. 其他收入（杂项收入）
**计算公式**：
```
其他收入 = 房间收入 × 5%
```

**说明**：包括会议室租赁、停车费、商务中心等其他收入

### 4. 总收入
**计算公式**：
```
总收入 = 房间收入 + 服务收入 + 其他收入
总收入 = 房间收入 × (1 + 10% + 5%) = 房间收入 × 1.15
```

## 💸 支出计算公式

### 1. 员工工资（人力成本）
**计算公式**：
```
日员工工资 = Σ(员工实际工资) / 30
员工实际工资 = 基础工资 × (1 + 工龄 × 10%)
```

**基础工资标准**：
- 初级员工：¥3,000/月
- 中级员工：¥5,000/月
- 高级员工：¥8,000/月
- 特级员工：¥15,000/月

**工龄计算**：
```
工龄 = (当前游戏日期 - 员工入职日期) / 365年
```

### 2. 房间维护费（设施维护）
**计算公式**：
```
日房间维护费 = 房间总数 × 10元/间/日
```

**说明**：包括房间清洁、设施维修、用品补充等费用

### 3. 水电费（公用事业）
**计算公式**：
```
日水电费 = 房间总数 × 5元/间/日
```

**说明**：包括电费、水费、燃气费、网络费等公用事业费用

### 4. 营销费用（推广成本）
**计算公式**：
```
日营销费用 = 日收入 × 2%
```

**说明**：包括广告投放、促销活动、公关活动等营销推广费用

### 5. 其他支出（杂项费用）
**计算公式**：
```
日其他支出 = 日收入 × 1%
```

**说明**：包括办公用品、保险费、税费、管理费等其他运营费用

### 6. 总支出
**计算公式**：
```
总支出 = 员工工资 + 房间维护费 + 水电费 + 营销费用 + 其他支出
```

## 📈 利润计算公式

### 1. 日利润
**计算公式**：
```
日利润 = 日收入 - 日支出
```

### 2. 月利润
**计算公式**：
```
月利润 = Σ(当月每日利润)
```

**备用估算公式**（无财务记录时）：
```
月利润 = 日利润估算 × 30天
```

## 🏗️ 建设费用计算公式

### 1. 房间建设费用
**计算公式**：
```
建设费用 = 房间单价 × 10 × 建设数量
```

**房间单价标准**：
- 单人间：¥300/晚 → 建设费¥3,000/间
- 标准间：¥500/晚 → 建设费¥5,000/间
- 大床房：¥700/晚 → 建设费¥7,000/间
- 家庭房：¥1,000/晚 → 建设费¥10,000/间
- 商务间：¥1,500/晚 → 建设费¥15,000/间
- 行政间：¥2,000/晚 → 建设费¥20,000/间
- 豪华间：¥3,000/晚 → 建设费¥30,000/间
- 总统套房：¥5,000/晚 → 建设费¥50,000/间
- 皇家套房：¥8,000/晚 → 建设费¥80,000/间
- 总统别墅：¥15,000/晚 → 建设费¥150,000/间
- 皇宫套房：¥30,000/晚 → 建设费¥300,000/间

### 2. 房型解锁费用
**计算公式**：
```
解锁费用 = 房间单价 × 10（包含1间房的建设费）
```

## 👥 员工相关费用计算公式

### 1. 员工招聘费用
**计算公式**：
```
招聘费用 = 基础招聘费 × 招聘数量
```

**基础招聘费标准**：
- 初级员工：¥10,000/人
- 中级员工：¥30,000/人
- 高级员工：¥80,000/人
- 特级员工：¥200,000/人

### 2. 员工解雇赔偿
**计算公式**：
```
解雇赔偿 = 员工实际工资 × (工龄 + 1)
员工实际工资 = 基础工资 × (1 + 工龄 × 10%)
```

## 🏨 酒店升级费用计算公式

### 1. 升级费用标准
**计算公式**：
```
升级费用 = 固定费用（根据目标等级）
```

**升级费用标准**：
- 1星→2星：¥1,000,000
- 2星→3星：¥2,500,000
- 3星→4星：¥5,000,000
- 4星→5星：¥10,000,000
- 5星→6星：¥20,000,000
- 6星→7星：¥40,000,000
- 7星→8星：¥80,000,000
- 8星→9星：¥150,000,000

### 2. 升级条件
除费用外，还需满足：
- **声望要求**：每级需要相应声望值
- **运营天数**：每级需要最少运营天数
- **客户满意度**：每级需要最低满意度

## 📊 计算示例

### 示例：2星酒店日收支计算
**房间配置**：
- 单人间：20间 × ¥300/晚 × 77%入住率 = ¥4,620
- 标准间：15间 × ¥500/晚 × 82%入住率 = ¥6,150

**收入计算**：
- 房间收入：¥4,620 + ¥6,150 = ¥10,770
- 服务收入：¥10,770 × 10% = ¥1,077
- 其他收入：¥10,770 × 5% = ¥539
- **总收入**：¥12,386

**支出计算**：
- 员工工资：5名员工，平均¥4,000/月 = ¥667/日
- 房间维护：35间 × ¥10/间/日 = ¥350
- 水电费：35间 × ¥5/间/日 = ¥175
- 营销费用：¥12,386 × 2% = ¥248
- 其他支出：¥12,386 × 1% = ¥124
- **总支出**：¥1,564

**利润计算**：
- **日利润**：¥12,386 - ¥1,564 = ¥10,822
- **月利润**：¥10,822 × 30 = ¥324,660

## 🔧 技术实现要点

### 1. 入住率四舍五入
```python
occupancy_rate_percent = calculate_stable_occupancy_rate(hotel, room.type, hotel.date)
occupancy_rate_rounded = round(occupancy_rate_percent)  # 四舍五入到整数
occupancy_rate = occupancy_rate_rounded / 100
```

### 2. 收入确保整数
```python
actual_income = room_count * room_price * occupancy_rate
# 由于入住率已四舍五入，收入自动为整数（当房价为整数时）
```

### 3. 财务记录精度
- 所有金额以**整数**形式存储（单位：元）
- 百分比计算保留**1位小数**显示
- 入住率计算时先**四舍五入**再应用

这样确保了所有收入都是合理的整数，避免了小数点导致的不规整金额。

## 🏆 声望值计算公式

### 1. 声望值变化因素
**计算公式**：
```
每日声望变化 = 满意度影响 + 财务表现影响 + 特殊事件影响
```

### 2. 满意度对声望的影响（等级制优化版）
**计算公式**：
```
满意度等级 = get_satisfaction_level(满意度分数)
声望变化 += 满意度等级对应的声望值

满意度等级对应声望值：
- 完美 (90-100分): +12声望
- 卓越 (80-89分): +8声望
- 优秀 (70-79分): +5声望
- 良好 (60-69分): +2声望
- 一般 (50-59分): 0声望
- 较差 (40-49分): -1声望
- 很差 (30-39分): -3声望
- 极差 (0-29分): -5声望
```

**等级制优化说明**：
- 基于满意度等级而非具体分数，更加直观
- 60分以上开始获得正向声望，符合游戏平衡
- 等级差距明显，激励玩家提升满意度等级
- 最高等级获得更多声望奖励，体现卓越服务价值

### 3. 财务表现对声望的影响（等级制优化版）
**计算公式**：
```
日利润 = 当日总收入 - 当日总支出
财务表现等级 = get_financial_performance_level(日利润)
声望变化 += 财务表现等级对应的声望值

财务表现等级对应声望值：
- 超盈 (>1,000,000): +15声望
- 巨盈 (500,000-1,000,000): +10声望
- 大盈 (100,000-500,000): +6声望
- 中盈 (50,000-100,000): +4声望
- 小盈 (10,000-50,000): +2声望
- 微盈 (0-10,000): +1声望
- 微亏 (-10,000-0): 0声望
- 轻微亏损 (-50,000--10,000): -1声望
- 中等亏损 (-100,000--50,000): -3声望
- 重大亏损 (-500,000--100,000): -5声望
- 严重亏损 (<-500,000): -8声望
```

**等级制优化说明**：
- 基于财务表现等级，更加精细化管理
- 盈利等级越高，声望奖励越丰厚
- 亏损等级越严重，声望惩罚越重
- 激励玩家追求更高的财务表现等级

### 4. 声望等级划分
**计算公式**：
```
声望等级 = min(10, 声望值 // 1000)
```

**等级对应表**：
- **0-499**：默默无闻 (等级0)
- **500-1999**：小有名气 (等级1)
- **2000-4999**：知名酒店 (等级2)
- **5000-9999**：著名品牌 (等级5)
- **10000-19999**：行业标杆 (等级10)
- **20000-49999**：行业领袖 (等级20)
- **50000-99999**：国际知名 (等级50)
- **100000+**：传奇酒店 (等级100)

### 5. 声望等级效果
**入住率加成公式**：
```
声望入住率加成 = 声望等级 × 1%
```

**其他效果**：
- 影响员工招聘成功率
- 影响随机事件的正面概率
- 解锁特殊成就和功能

### 6. 特殊声望奖励
**酒店升级奖励**：
```
升级声望奖励 = 100 × 新等级
```

**成就完成奖励**：
```
成就声望奖励 = 根据成就难度确定（10-1000不等）
```

**随机事件影响**：
```
随机事件声望影响 = ±10 到 ±100（根据事件类型）
```

## 📊 声望计算示例

### 示例：2星酒店声望计算
**当前状态**：
- 满意度：75分
- 日利润：¥25,000
- 当前声望：1,200

**每日声望变化计算**：
- 满意度影响：75分 >= 70 → +1声望
- 财务表现：¥25,000 > ¥10,000 → +1声望
- 总变化：+2声望/天

**一个月后**：
- 新声望值：1,200 + (2 × 30) = 1,260
- 声望等级：1,260 // 1000 = 1级（小有名气）
- 入住率加成：1 × 1% = +1%

这样确保了声望系统的透明度和可预测性。

## 😊 满意度计算公式

### 1. 满意度基础计算
**计算公式**：
```
满意度 = 基础满意度 + 酒店等级加成 + 房间等级加成 + 部门效果 + 繁忙度影响
```

### 2. 各项计算细节
**基础满意度**：
```
基础满意度 = 50分
```

**酒店等级加成**：
```
等级加成 = 酒店星级 × 1分
```

**房间等级加成**：
```
皇宫套房：每间 +10分
总统别墅：每间 +8分
皇家套房：每间 +6分
总统套房：每间 +4分
豪华间：每间 +2分
其他房型：每间 +0分
```

**部门特殊效果**：
```
if 康养部已解锁: 满意度 +5分
if 娱乐部已解锁: 满意度 +3分
if 会议部已解锁: 满意度 +2分
```

**繁忙度影响**：
```
if 平均繁忙度 < 50%: 满意度 +10分
elif 平均繁忙度 <= 80%: 满意度 +0分
elif 平均繁忙度 <= 100%: 满意度 -5分
else: 满意度 -15分  # 过度繁忙
```

### 3. 满意度限制
**计算公式**：
```
最终满意度 = max(0, min(100, 计算满意度))
```

## 📊 员工平均等级计算公式

### 1. 员工等级数值化
**等级对应数值**：
```
初级 = 1
中级 = 2
高级 = 3
特级 = 4
```

### 2. 平均等级计算
**计算公式**：
```
员工平均等级 = Σ(员工等级数值) / 员工总数
```

**示例计算**：
- 初级员工：5人 × 1 = 5
- 中级员工：3人 × 2 = 6
- 高级员工：2人 × 3 = 6
- 特级员工：1人 × 4 = 4
- 平均等级 = (5+6+6+4) / 11 = 1.9级

## 🎯 营销管理品牌知名度计算公式

### 1. 品牌知名度基础计算
**计算公式**：
```
品牌知名度 = 基础知名度 + 声望影响 + 营销活动影响 + 酒店等级影响
```

### 2. 各项计算细节
**基础知名度**：
```
基础知名度 = 20%
```

**声望影响**：
```
声望知名度加成 = min(30, 声望等级 × 3)%
```

**营销活动影响**：
```
活跃营销活动知名度加成 = Σ(活跃营销活动效果) × 0.5%
```

**酒店等级影响**：
```
等级知名度加成 = 酒店星级 × 2%
```

### 3. 知名度限制
**计算公式**：
```
最终品牌知名度 = min(100, 基础知名度 + 各项加成)%
```

**示例计算**：
- 基础知名度：20%
- 声望影响：声望等级5 × 3% = 15%
- 营销活动：2个活跃活动，效果20% × 0.5% = 10%
- 酒店等级：3星 × 2% = 6%
- 最终知名度：20% + 15% + 10% + 6% = 51%

## 📈 营销活动效果计算公式

### 1. 营销活动对入住率的影响
**计算公式**：
```
营销入住率加成 = Σ(活跃营销活动效果)%
```

### 2. 营销活动费用计算
**计算公式**：
```
营销活动费用 = 基础费用 × 酒店等级系数
酒店等级系数 = 1 + (酒店星级 - 1) × 0.2
```

**费用递增标准**：
- 网络推广：¥10,000 → 效果+5%
- 电视广告：¥50,000 → 效果+12%
- 线下推广：¥100,000 → 效果+20%
- 商务活动：¥200,000 → 效果+30%
- 品牌合作：¥500,000 → 效果+50%

### 3. 营销活动持续时间
**计算公式**：
```
活动持续时间 = 基础持续时间 + 随机浮动(±2天)
```

这样确保了所有计算都有明确的公式和标准。
