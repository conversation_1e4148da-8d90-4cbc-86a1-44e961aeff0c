import sqlite3
import os
from app import create_app, db
from app.models import Hotel, Employee, Department, Room, FinancialRecord, Achievement, GameSetting

def migrate_database():
    """迁移数据库结构"""
    app = create_app()
    
    with app.app_context():
        # 获取数据库路径
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        print(f"数据库路径: {db_path}")
        
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 检查 achievement 表是否存在 achieved 字段
            cursor.execute("PRAGMA table_info(achievement)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            
            print(f"当前 achievement 表字段: {column_names}")
            
            # 如果缺少 achieved 字段，则添加
            if 'achieved' not in column_names:
                print("添加 achieved 字段...")
                cursor.execute("ALTER TABLE achievement ADD COLUMN achieved BOOLEAN DEFAULT FALSE")
            
            # 如果缺少 achieved_date 字段，则添加
            if 'achieved_date' not in column_names:
                print("添加 achieved_date 字段...")
                cursor.execute("ALTER TABLE achievement ADD COLUMN achieved_date DATE")
            
            # 提交更改
            conn.commit()
            print("数据库迁移完成!")
            
        except Exception as e:
            print(f"迁移过程中出现错误: {e}")
            conn.rollback()
        finally:
            conn.close()

if __name__ == '__main__':
    migrate_database()